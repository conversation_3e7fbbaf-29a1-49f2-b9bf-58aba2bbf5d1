﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using RFQ.Dao;
using RFQ.Dao.Model;
using System.Web.SessionState;
using Newtonsoft.Json;
using System.Runtime.Serialization.Json;
using System.IO;
using System.Text;

namespace WebApplication1
{
    /// <summary>
    /// Handler1 的摘要说明
    /// </summary>
    public class HomeController : IHttpHandler, IRequiresSessionState
    {

        public void ProcessRequest(HttpContext context)
        {
            context.Response.ContentType = "Application/json";
            User userinfo = (User)HttpContext.Current.Session["UserInfo"];
            if(userinfo ==null)
            {
                context.Response.Write(null);
                return;
            }
            var user = new BaseRepository<User>().FindSingle(u => u.UserName == userinfo.UserName);
            var data = JsonConvert.SerializeObject(SystemMenu(user.UserRole));
            //var data = JsonDataContractJsonSerializer.ObjectToJson(SystemMenu());
            context.Response.Write(data);
        }

        public bool IsReusable
        {
            get
            {
                return false;
            }
        }

        public MenusInfoResultDTO SystemMenu(string userrole)
        {
            var typeid = new BaseRepository<RoleAuth>().Find(s => s.RoleName == userrole).ToList();
            
            List<Module> systemMenuEntities = new List<Module>();

            systemMenuEntities = new BaseRepository<Module>().Find(null).ToList().Where(s => typeid.Select(t=>t.TypeId).Contains(s.TypeId)).Where(s=>s.Target!= "").ToList();


            SystemMenu rootNode = new SystemMenu()
            {
                Id = 0,
                icon = "",
                href = "",
                title = "根目录",
            };

            GetTreeNodeListByNoLockedDTOArray(systemMenuEntities.ToArray(), rootNode);

            MenusInfoResultDTO menusInfoResultDTO = new MenusInfoResultDTO();
            menusInfoResultDTO.menuInfo = rootNode.child;
            menusInfoResultDTO.logoInfo = new LogoInfo();
            menusInfoResultDTO.homeInfo = new HomeInfo();

            return menusInfoResultDTO;
        }

        /// <summary>
        /// 递归处理数据
        /// </summary>
        /// <param name="systemMenuEntities"></param>
        /// <param name="rootNode"></param>
        public static void GetTreeNodeListByNoLockedDTOArray(Module[] systemMenuEntities, SystemMenu rootNode)
        {
            if (systemMenuEntities == null || systemMenuEntities.Count() <= 0)
            {
                return;
            }

            var childreDataList = systemMenuEntities.Where(p => p.Pid == rootNode.Id).Where(p=>p.Target!="").OrderBy(p=>p.Sort);
            if (childreDataList != null && childreDataList.Count() > 0)
            {
                rootNode.child = new List<SystemMenu>();

                foreach (var item in childreDataList)
                {
                    SystemMenu treeNode = new SystemMenu()
                    {
                        Id = item.TypeId,
                        icon = item.Icon,
                        href = item.Href,
                        title = item.Title,
                    };
                    rootNode.child.Add(treeNode);
                }

                foreach (var item in rootNode.child)
                {
                    GetTreeNodeListByNoLockedDTOArray(systemMenuEntities, item);
                }
            }
        }


    }
    class JsonDataContractJsonSerializer
    {
        public static string ObjectToJson(object obj)
        {
            DataContractJsonSerializer serializer = new DataContractJsonSerializer(obj.GetType());
            MemoryStream stream = new MemoryStream();
            serializer.WriteObject(stream, obj);
            byte[] dataBytes = new byte[stream.Length];
            stream.Position = 0;
            stream.Read(dataBytes, 0, (int)stream.Length);
            return Encoding.UTF8.GetString(dataBytes);
        }
    }

    /// <summary>
    /// 菜单结果对象
    /// </summary>
    public class MenusInfoResultDTO
    {
        /// <summary>
        /// 权限菜单树
        /// </summary>
        public List<SystemMenu> menuInfo { get; set; }

        /// <summary>
        /// logo
        /// </summary>
        public LogoInfo logoInfo { get; set; }

        /// <summary>
        /// Home
        /// </summary>
        public HomeInfo homeInfo { get; set; }
    }

    public class LogoInfo
    {
        public string title { get; set; } = "RFQ";
        public string image { get; set; } = "images/logo.png";
        public string href { get; set; } = "index#/main.aspx?t=1";
    }

    public class HomeInfo
    {
        public string title { get; set; } = "首页";
        public string href { get; set; } = "main.aspx?t=1";

    }


    /// <summary>
    /// 树结构对象
    /// </summary>
    public class SystemMenu
    {
        /// <summary>
        /// 数据ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 父级ID
        /// </summary>
        public long PId { get; set; }

        /// <summary>
        /// 节点名称
        /// </summary>
        public string title { get; set; }

        /// <summary>
        /// 节点地址
        /// </summary>
        public string href { get; set; }

        /// <summary>
        /// 新开Tab方式
        /// </summary>
        public string target { get; set; } = "_self";

        /// <summary>
        /// 菜单图标样式
        /// </summary>
        public string icon { get; set; }



        /// <summary>
        /// 子集
        /// </summary>
        public List<SystemMenu> child { get; set; }
    }
}