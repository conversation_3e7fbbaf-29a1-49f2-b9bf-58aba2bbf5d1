﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="TemplateManage.aspx.cs" Inherits="WebApplication1.DataManager.TemplateManage1" %>

<head>
<title>模板管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../lib/layui-v2.9.21/css/layui.css" media="all">
    <link rel="stylesheet" href="../css/public.css" media="all">
</head>
<style>
    .container{
        display:flex;
        justify-content:center;
        align-items:center;
    }
    .layui-form-label{
        width:180px !important; 
    }
    </style>
<div class="layuimini-container layuimini-page-anim">
    <div class="layuimini-main">
        <fieldset class="table-search-fieldset">
            <legend>搜索信息</legend>
            <div style="margin: 20px 10px 10px 10px">
                <form class="layui-form layui-form-pane" action="">
                    <div class="layui-form-item">
                        
                        <div class="layui-inline">
                            <label class="layui-form-label">客户编号</label>
                            <div class="layui-input-inline">
                                <input type="text" name="CustomerCode" autocomplete="off" class="layui-input">
                            </div>
                        </div>

                        <div class="layui-inline">
                            <button type="submit" class="layui-btn layui-btn-primary"  lay-submit lay-filter="data-search-btn"><i class="layui-icon"></i> 搜 索</button>
                        </div>
                    </div>
                </form>
            </div>
        </fieldset>
        

        <script type="text/html" id="toolbarDemo">
            <div class="layui-btn-container">
                <button class="layui-btn layui-btn-normal layui-btn-sm data-add-btn" lay-event="add"> 添加 </button>
                <button class="layui-btn layui-btn-sm layui-btn-danger data-delete-btn" lay-event="delete"> 删除 </button>
            </div>
        </script>

        <table class="layui-hide" id="currentTableId" lay-filter="currentTableFilter"></table>

        <script type="text/html" id="currentTableBar">
            <a class="layui-btn layui-btn-normal layui-btn-xs data-count-edit" lay-event="field">查看映射关系</a>
            <%--<a class="layui-btn layui-btn-xs layui-btn-danger data-count-delete" lay-event="edit">编辑</a>--%>
        </script>
        
    </div>
</div>
   <script src="../lib/layui-v2.9.21/layui.js" charset="utf-8"></script>
<script>

    layui.use(['form', 'table', 'element'], function () {
        var $ = layui.jquery,
            form = layui.form,
            table = layui.table;

        //var addData = [
        //    { id: 1, CustomerCode: '张三', HeadLine: '12345678901', ContentLine: '<EMAIL>' },
        //    { id: 2, CustomerCode: '李四', HeadLine: '23456789012', ContentLine: '<EMAIL>' },
        //    { id: 3, CustomerCode: '王五', HeadLine: '34567890123', ContentLine: '<EMAIL>' }
        //];

        table.render({
            elem: '#currentTableId',
            url: '../ashx/TemplateControl.ashx?action=QueryConfig',
            //data: addData,
            toolbar: '#toolbarDemo',
            method: 'post',
            height: 520,
            defaultToolbar: ['filter', 'exports', 'print', {
                title: '提示',
                layEvent: 'LAYTABLE_TIPS',
                icon: 'layui-icon-tips'
            }],
            cols: [[
                { type: "checkbox", width: 50 },
                { field: 'CustomerCode', width: 150, title: '客户编号', sort: true },
                { field: 'HeadLine', minWidth: 150, title: '模板表头行' },
                { field: 'ContentLine', minWidth: 150, title: '模板内容行', sort: true },
                { field: 'QtyStartLine', minWidth: 150, title: '用量开头列', sort: true },
                { title: '操作', minWidth: 200, toolbar: '#currentTableBar', align: "center" }
            ]],
            limits: [10, 15, 20, 25, 50, 100],
            limit: 10,
            page: true,
            skin: 'line',

        });



        // 监听搜索操作
        form.on('submit(data-search-btn)', function (data) {
            var result = data.field;
            //console.log(result.Customer);
            //layer.alert(result, {
            //    title: '最终的搜索信息'
            //});

            //执行搜索重载
            table.reload('currentTableId', {
                page: {
                    curr: 1
                }
                , where: result,
                error: {


                }

            }, 'data');

            return false;
        });

        /**
         * toolbar事件监听
         */
        table.on('toolbar(currentTableFilter)', function (obj) {
            if (obj.event === 'add') {   // 监听添加操作
                

                var index = layer.open({
                    title: '新建Csv配置信息',
                    type: 2,
                    shade: 0.2,
                    maxmin: true,
                    shadeClose: true,
                    area: ['60%', '60%'],
                    content: '../DataManager/AddCsvConfig.aspx',
                    success: function (layero, index) {
                        var iframeWindow = window['layui-layer-iframe' + index];
                        var iframeDoc = $(layero).find('iframe')[0].contentWindow.document;
                        
                        //console.log(data);
                        
                        //监听提交
                        iframeWindow.layui.form.on('submit(saveBtn)', function (data) {
                            var field = data.field;
                            console.log(field);
                            $.ajax({
                                url: '../ashx/TemplateControl.ashx?action=Add',
                                type: 'post',
                                data: field,
                                success: function (data) {
                                    console.log(data);
                                    var objdata = eval("(" + data + ")")
                                    if (objdata.result == "success") {
                                        //console.log(field);
                                        layer.msg(objdata.msg, { icon: 1, time: 2000 }, function () {
                                            layer.close(index);
                                            table.reload('currentTableId')
                                        })
                                    }
                                    else {
                                        layer.alert(objdata.msg);
                                    }

                                },
                                error: function (data) {
                                    layer.alert(objdata.msg);
                                }
                            });
                            return false;

                        });

                    },
                });
                $(window).on("resize", function () {
                    layer.full(index);
                });
            } else if (obj.event === 'delete') {  // 监听删除操作
                var checkStatus = table.checkStatus('currentTableId')
                    , data = checkStatus.data;
                console.log(data);

                if (data.length == 0) {
                    layer.msg('请选择要删除的项', { icon: 0 });
                    return;
                }
                var nos = data.map(function (item) {
                    return item.CustomerCode;
                }).join(',');
                $.ajax({
                    url: '../ashx/TemplateControl.ashx?action=BatchDelete',
                    type: 'POST',
                    data: { nos: nos },
                    success: function (response) {
                        var res = JSON.parse(response);
                        console.log(res);
                        if (res.result === 'success') {
                            layer.msg('删除成功', { icon: 1 });
                            // 重新加载表格数据
                            table.reload('currentTableId', {
                                
                                done: function (res, curr, count) {
                                    if (curr > 1 && res.data.length === 0) {
                                        curr = curr - 1;
                                        table.reload('currentTableId', { page: { curr: curr }, })
                                    }
                                }
                            });
                        } else {
                            layer.msg('删除失败: ' + res.msg, { icon: 2 });
                        }
                    },
                    error: function () {
                        layer.msg('删除请求失败', { icon: 2 });
                    }
                });
                //layer.alert(JSON.stringify(data));
            }
        });

        //监听表格复选框选择
        table.on('checkbox(currentTableFilter)', function (obj) {
            //console.log(obj)
        });

        table.on('tool(currentTableFilter)', function (obj) {
            var data = obj.data;
            //if (obj.event === 'edit') {

            //    var content = miniPage.getHrefContent('DataManager/EditCsvConfig.aspx');
            //    var openWH = miniPage.getOpenWidthHeight();

            //    var index = layer.open({
            //        title: '编辑Excel配置',
            //        type: 1,
            //        shade: 0.2,
            //        maxmin: true,
            //        shadeClose: true,
            //        area: [openWH[0] + 'px', openWH[1] + 'px'],
            //        offset: [openWH[2] + 'px', openWH[3] + 'px'],
            //        content: '../DataManager/EditCsvConfig.aspx',
            //        success: function (layero, index) {
            //            var iframeWindow = window['layui-layer-iframe' + index];
            //            iframeWindow.layui.form.val("editform", data);
            //            var code = data.CustomerCode;
            //            //console.log(data);
                       
            //            //监听提交
            //            iframeWindow.layui.form.on('submit(saveBtn)', function (data) {
            //                var field = data.field;
            //                console.log(field);
            //                $.ajax({
            //                    url: '../ashx/TemplateControl.ashx?action=Update&CustomerCode='+code,
            //                    type: 'post',
            //                    data: field,
            //                    success: function (data) {
            //                        var objdata = eval("(" + data + ")")
            //                        if (objdata.result == "success") {
            //                            //console.log(field);
            //                            layer.msg(objdata.msg, { icon: 1, time: 2000 }, function () {
            //                                layer.close(index);
            //                                table.reload('currentTableId')
            //                            })
            //                        }
            //                        else {
            //                            layer.alert(objdata.msg);
            //                        }

            //                    },
            //                    error: function (data) {
            //                        layer.alert(objdata.msg);
            //                    }
            //                });
            //                return false;

            //            });
            //        },
            //    });
            //    $(window).on("resize", function () {
            //        layer.full(index);
            //    });
            //    return false;
            //} else 
            if (obj.event === 'field') {
                

                var index = layer.open({
                    title: '编辑映射关系',
                    type: 2,
                    shade: 0.2,
                    maxmin: true,
                    shadeClose: true,
                    area: ['100%', '100%'],
                    content: '../DataManager/FieldManage.aspx',
                    success: function (layero, index) {
                        // 获取iframe页面的窗口对象
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        // 获取iframe页面的document对象
                        var iframeDoc = $(layero).find('iframe')[0].contentWindow.document;
                        console.log(data);
                        var code = data.CustomerCode;
                        //form.val("addform");
                         
                        iframeWin.layui.table.render({
                                elem: '#ruleTableid',
                                url: '../ashx/TemplateControl.ashx?action=QueryRule&CustomerCode='+code,
                                toolbar: '#toolbarrule',
                                method: 'post',
                                height:520,
                                cols: [[
                                    { type: "checkbox", width: 50 },
                                    { field: 'CsvHeader', minWidth: 150, title: 'Excel列' },
                                    { field: 'DatabaseField', minWidth: 150, title: '数据库列', sort: true },
                                ]],
                                limit:10,
                                page: true,
                                skin: 'line',

                            });
                        form.render('select');
                        iframeWin.layui.table.on('toolbar(ruleTableFilter)', function (obj) {
                            if (obj.event == "delete") {
                                var checkStatus = table.checkStatus('ruleTableid')
                                    , data = checkStatus.data;
                                console.log(data);

                                if (data.length == 0) {
                                    layer.msg('请选择要删除的项', { icon: 0 });
                                    return;
                                }
                                var nos = data.map(function (item) {
                                    return item.CsvHeader;
                                }).join(',');
                                $.ajax({
                                    url: '../ashx/TemplateControl.ashx?action=DeleteRule&CustomerCode='+code,
                                    type: 'POST',
                                    data: { nos: nos },
                                    success: function (response) {
                                        var res = JSON.parse(response);
                                        console.log(res);
                                        if (res.result === 'success') {
                                            layer.msg('删除成功', { icon: 1 });
                                            // 重新加载表格数据
                                            table.reload('ruleTableid', {
                                                
                                                done: function (res, curr, count) {
                                                    if (curr > 1 && res.data.length === 0) {
                                                        curr = curr - 1;
                                                        table.reload('ruleTableid', { page: { curr: curr }, })
                                                    }
                                                }
                                            });
                                        } else {
                                            layer.msg('删除失败: ' + res.msg, { icon: 2 });
                                        }
                                    },
                                    error: function () {
                                        layer.msg('删除请求失败', { icon: 2 });
                                    }
                                });
                            }
                        });

                        //监听提交
                        iframeWin.layui.form.on('submit(saveRule)', function (data) {
                            var field = data.field;
                            console.log(field);
                            $.ajax({
                                url: '../ashx/TemplateControl.ashx?action=AddRule&CustomerCode='+code,
                                type: 'post',
                                data: field,
                                success: function (data) {
                                    console.log(data);
                                    var objdata = eval("(" + data + ")")
                                    if (objdata.result == "success") {
                                        //console.log(field);
                                        layer.msg(objdata.msg, { icon: 1, time: 2000 }, function () {
                                            //layer.close(index);
                                            table.reload('ruleTableid')
                                        })
                                    }
                                    else {
                                        layer.alert(objdata.msg);
                                    }

                                },
                                error: function (data) {
                                    layer.alert(objdata.msg);
                                }
                            });
                            return false;

                        });

                    },
                });
                $(window).on("resize", function () {
                    layer.full(index);
                });
                
            }
        });
        //配置读取规则按钮点击事件
        // $('#setConfig').on('click', function () {
        //     if ($('#CustomerCode').val() == "") {
        //         layer.msg('请先选择客户编号！', { icon: 2 });
        //     }
        //     else {
        //         var content = miniPage.getHrefContent('DataManager/EditCsvConfig.aspx');
        //         var code = $('#CustomerCode').val();

        //         var index = layer.open({
        //             title: '配置读取规则',
        //             type: 1,
        //             shade: 0.2,
        //             maxmin: true,
        //             shadeClose: true,
        //             area: ['600px', '700px'],
        //             offset: ['150px', '550px'],
        //             content: content,
        //             success: function (layero, index) {
        //                 $.ajax({
        //                     url: '../ashx/TemplateControl.ashx?action=QuerySingleConfig&CustomerCode=' + code + '&RFQNo=' + RFQNo,
        //                     type: 'post',
        //                     success: function (data) {
        //                         var objdata = eval("(" + data + ")")
        //                         form.val("editform", objdata);

        //                     },
        //                     error: function (data) {
        //                         layer.alert("加载数据失败");
        //                     }
        //                 });


        //                 form.on('submit(saveBtn)', function (data) {
        //                     $('button').prop('disabled', true);  // 禁用所有按钮
        //                     var index2 = layer.load(1, { shade: [0.5, '#fff'] });  // 显示加载动画
        //                     var field = data.field;
        //                     console.log(field);
        //                     $.ajax({
        //                         url: '../ashx/TemplateControl.ashx?action=Update&CustomerCode=' + code + '&RFQNo=' + RFQNo,
        //                         type: 'post',
        //                         data: field,
        //                         success: function (data) {
        //                             var objdata = eval("(" + data + ")")
        //                             if (objdata.result == "success") {
        //                                 //console.log(field);
        //                                 layer.msg(objdata.msg, { icon: 1, time: 2000 }, function () {
        //                                     layer.close(index);
        //                                 })
        //                             }
        //                             else {
        //                                 layer.alert(objdata.msg);
        //                             }
        //                             layer.close(index2);  // 关闭加载动画
        //                             $('button').prop('disabled', false);  // 恢复所有按钮

        //                         },
        //                         error: function (data) {
        //                             layer.alert(objdata.msg);
        //                             layer.close(index2);  // 关闭加载动画
        //                             $('button').prop('disabled', false);  // 恢复所有按钮
        //                         }
        //                     });
        //                     return false;

        //                 });
        //             }
        //         });
        //         $(window).on("resize", function () {
        //             layer.full(index);
        //         });
        //         return false;
        //     }
        // });

    });</script>
