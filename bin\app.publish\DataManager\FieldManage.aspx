﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="FieldManage.aspx.cs" Inherits="WebApplication1.DataManager.FieldManage" %>

<!DOCTYPE html>
<head>
    <meta charset="utf-8">
    <title>规则设置</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../lib/layui-v2.9.21/css/layui.css" media="all">
    <link rel="stylesheet" href="../css/public.css" media="all">
</head>
<style>
        
    </style>

        <form class="layui-form" id="ruleForm">
            <div class="layuimini-main">
                <div class="layui-form layuimini-form">
            <div class="layui-form-item">
                <label class="layui-form-label">报价BOM基本信息</label>
                <div class="layui-input-block">
                    <input type="text" name="excelColumn" required  lay-verify="required" placeholder="请输入报价BOM的基本信息，如客户编号对应的列的列名" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                
                <label class="layui-form-label">数据库列</label>
                    <div class="layui-input-inline">
                        <select name="databaseColumn" lay-verify="required" lay-search="" >
                            <option value="">直接选择或搜索选择</option>
                            <option value="CUST_PARTNUMBER">CUST_PARTNUMBER</option>
                            <option value="BOM_DESC">BOM_DESC</option>
                            <option value="REF_BOM">REF_BOM</option>
                            <option value="UOM_BOM">UOM_BOM</option>
                            <option value="LOA_PRICE">LOA_PRICE</option>
                            <option value="CONTROL_FLAG">CONTROL_FLAG</option>
                            <option value="ITEM">ITEM</option>
                            <option value="NOTES">NOTES</option>
                            <option value="MANUFACTURER">MANUFACTURER</option>
                            <option value="MAN_PARTNUMBER">MAN_PARTNUMBER</option>
                            <option value="EAU">EAU</option>
                            <option value="TOOL_COST">TOOL_COST</option>
                            <option value="TOOL_CURR">TOOL_CURR</option>
                        </select>
                    </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="saveRule">添加规则</button>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <button type="button" id="uploadtemple" class="layui-btn layui-btn-primary" lay-filter="data-upload-btn">上传模板文件</button>
                </div>
                
            </div>
             </div>
          </div>
        </form>

        <script type="text/html" id="toolbarrule">
            <div class="layui-btn-container">
                <button class="layui-btn layui-btn-sm layui-btn-danger data-delete-btn" lay-event="delete"> 删除 </button>
            </div>
        </script>
        <table id="ruleTableid" lay-filter="ruleTableFilter"></table>

        <div class="layui-form-item">
                <div class="layui-input-block">
                    <button id="submitRule" class="layui-btn">保存规则</button>
                </div>
                
            </div>
<script src="../lib/layui-v2.9.21/layui.js" charset="utf-8"></script>



 