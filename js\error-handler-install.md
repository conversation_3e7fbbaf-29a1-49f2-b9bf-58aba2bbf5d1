# 错误处理模块安装指南

## 快速开始

### 1. 文件部署

将 `error-handler.js` 文件放置到项目的 `js` 目录下：

```
项目根目录/
├── js/
│   ├── error-handler.js     ← 新增文件
│   └── navigate.js
├── lib/
│   └── layui-v2.9.21/
└── Flow/
    └── CustomerTrans.aspx
```

### 2. 引入脚本

在需要使用错误处理的页面中引入脚本：

```html
<!-- 必须在layui之后引入 -->
<script src="../lib/layui-v2.9.21/layui.js" charset="utf-8"></script>
<script src="../js/error-handler.js" charset="utf-8"></script>
```

### 3. 基本使用

```javascript
// 在layui.use中使用
layui.use(['jquery'], function() {
    var $ = layui.jquery;
    
    // AJAX请求示例
    $.ajax({
        url: '/api/example',
        type: 'POST',
        success: function(response) {
            try {
                var result = JSON.parse(response);
                if (result.code === 0) {
                    ErrorHandler.showSuccess('操作成功');
                } else {
                    ErrorHandler.showErrorDetails('操作名称', result.msg, '详细信息');
                }
            } catch (e) {
                ErrorHandler.handleParseError('操作名称', response, e);
            }
        },
        error: function(xhr, status, error) {
            ErrorHandler.handleAjaxError(xhr, status, error, '操作名称');
        }
    });
});
```

## 迁移现有代码

### 替换现有错误处理

**原来的代码：**
```javascript
success: function(data) {
    var res = eval("(" + data + ")");
    if (res.result == "success") {
        layer.msg('操作成功');
    } else {
        layer.alert(res.msg);
    }
},
error: function(data) {
    layer.alert("操作失败");
}
```

**新的代码：**
```javascript
success: function(data) {
    try {
        var res = JSON.parse(data);
        if (res.result == "success") {
            ErrorHandler.showSuccess('操作成功');
        } else {
            ErrorHandler.showErrorDetails('操作名称', res.msg || '操作失败', '服务器返回: ' + data);
        }
    } catch (e) {
        ErrorHandler.handleParseError('操作名称', data, e);
    }
},
error: function(xhr, status, error) {
    ErrorHandler.handleAjaxError(xhr, status, error, '操作名称');
}
```

### 批量替换步骤

1. **替换成功消息**
   ```javascript
   // 原来
   layer.msg('操作成功', {icon: 1});
   
   // 现在
   ErrorHandler.showSuccess('操作成功');
   ```

2. **替换简单错误**
   ```javascript
   // 原来
   layer.msg('操作失败', {icon: 2});
   
   // 现在
   ErrorHandler.showSimpleError('操作失败');
   ```

3. **替换详细错误**
   ```javascript
   // 原来
   layer.alert(errorMessage);
   
   // 现在
   ErrorHandler.showErrorDetails('操作名称', errorMessage, '详细信息');
   ```

## 项目集成清单

### 需要修改的文件类型

- [ ] **ASPX页面**: 添加脚本引用
- [ ] **JavaScript文件**: 替换错误处理逻辑
- [ ] **ASHX处理器**: 确保返回标准化的错误格式

### 标准化错误响应格式

确保后端返回统一的错误格式：

```json
{
    "code": 0,           // 0=成功, 1=失败
    "result": "success", // success/fail
    "msg": "操作成功",    // 用户友好的消息
    "data": {}          // 数据内容
}
```

### 推荐的页面结构

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>页面标题</title>
    <link rel="stylesheet" href="../lib/layui-v2.9.21/css/layui.css">
</head>
<body>
    <!-- 页面内容 -->
    
    <!-- 脚本引入 -->
    <script src="../lib/layui-v2.9.21/layui.js"></script>
    <script src="../js/navigate.js"></script>
    <script src="../js/error-handler.js"></script>
    
    <script>
        layui.use(['form', 'table', 'jquery'], function() {
            // 页面逻辑
        });
    </script>
</body>
</html>
```

## 测试验证

### 1. 功能测试

使用提供的测试页面验证功能：
- `test_error_handling.html` - 基础功能测试
- `example_usage.html` - 实际使用示例

### 2. 兼容性测试

确保在以下环境中正常工作：
- [ ] IE 9+
- [ ] Chrome
- [ ] Firefox
- [ ] Edge
- [ ] Safari

### 3. 集成测试

在实际页面中测试：
- [ ] 网络错误处理
- [ ] 服务器错误处理
- [ ] JSON解析错误处理
- [ ] 成功消息显示
- [ ] 错误信息复制功能

## 常见问题

### Q: 错误处理模块不工作？
A: 检查以下几点：
1. 确保在layui之后引入
2. 检查控制台是否有JavaScript错误
3. 确认layui.layer组件正常加载

### Q: 复制功能不工作？
A: 模块会自动降级处理，如果现代API不支持会使用传统方法

### Q: 如何自定义错误弹窗样式？
A: 可以通过配置选项自定义：
```javascript
ErrorHandler.showErrorDetails('操作', '错误', '详情', {
    title: '自定义标题',
    width: '500px',
    height: '400px'
});
```

### Q: 如何在不支持layui的环境中使用？
A: 模块会自动降级到使用原生alert，但建议在layui环境中使用以获得最佳体验

## 技术支持

如有问题，请联系开发团队或查看：
- `error-handler-readme.md` - 详细使用说明
- `example_usage.html` - 实际使用示例
- `test_error_handling.html` - 功能测试页面
