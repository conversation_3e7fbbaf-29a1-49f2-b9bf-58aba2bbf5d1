<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="FlowAssign.aspx.cs" Inherits="WebApplication1.AssignFlow.FormAssign" %>



<head>
    <meta charset="utf-8">
    <title>流程单查询及签核</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../lib/layui-v2.5.5/css/layui.css" media="all">
    <link rel="stylesheet" href="../css/public.css" media="all">
    <style>
        body {
            background-color: #f2f2f2;
            margin: 0;
            padding: 0;
        }
        .layui-form {
            padding: 20px;
            background-color: #fff;
            margin: 20px auto;
            max-width: 1200px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-radius: 4px;
        }
        .table-container {
            padding: 0 20px;
            margin: 0 auto;
            max-width: 1200px;
        }
        .layui-table-body {
            height: calc(100vh - 250px);
            overflow-y: auto;
        }
        .download-btn {
            margin-right: 5px;
        }
        .file-button {
            display: inline-block;
            padding: 6px 12px;
            margin-right: 10px;
            background-color: #1E9FFF;
            color: #fff;
            border-radius: 3px;
            cursor: pointer;
            border: none;
            transition: all 0.3s ease;
        }
        .file-button:hover {
            background-color: #0d8ae6;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        .layui-table-cell {
            height: 40px;
            line-height: 40px;
            padding: 0 15px;
        }
        .rfq-link {
            color: #1E9FFF;
            cursor: pointer;
            font-weight: 500;
        }
        .rfq-link:hover {
            color: #0d8ae6;
            text-decoration: underline;
        }
        /* 新增弹窗样式 */
        .flow-detail-popup .layui-layer-content > div {
        width: 100% !important;
    }
    
    .flow-detail-popup .flow-detail-container {
        width: 100% !important;
        max-width: none !important;
    }
    
    .flow-detail-popup .layui-card {
        width: 100% !important;
        margin-bottom: 15px !important;
    }
    
    .flow-detail-popup .layui-form-item {
        width: 100% !important;
        display: flex !important;
        flex-wrap: wrap !important;
        margin: 0 -10px !important;
        padding: 5px 0 !important;
    }
    
    .flow-detail-popup .layui-inline {
        width: 33.33% !important;
        padding: 0 10px !important;
        box-sizing: border-box !important;
        margin: 0 0 10px 0 !important;
        display: flex !important;
    }
    
    .flow-detail-popup .layui-form-label {
        width: 110px !important;
    }
    
    .flow-detail-popup .layui-input-inline {
        width: calc(100% - 110px) !important;
        margin-right: 0 !important;
    }
    
    .flow-detail-popup #bomDetailTable {
        width: 100% !important;
    }
    

.flow-detail-popup .layui-card:last-child {
    margin-bottom: 0;
}

.flow-detail-popup .layui-card-body .layui-form-item {
    display: flex;
    justify-content: center;
    align-items: center;
}

.flow-detail-popup .layui-btn {
    margin: 0 10px;
}
        /* 美化表格样式 */
        .layui-table {
            width: 100% !important;
            margin: 0 !important;
        }
        .layui-table thead tr {
            background-color: #f8f8f8;
        }
        .layui-table thead th {
            font-weight: bold;
            color: #333;
            border-bottom: 2px solid #e6e6e6;
        }
        .layui-table tbody tr:hover {
            background-color: #f5f7fa !important;
        }
        .layui-table td {
            border-bottom: 1px solid #eee;
        }
        /* 状态样式优化 */
        .status-badge {
            padding: 4px 8px;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-pending {
            background-color: #fff7e6;
            color: #FFB800;
        }
        .status-completed {
            background-color: #e6f7f2;
            color: #009688;
        }
        .status-unsent {
            background-color: #fff1f0;
            color: #FF5722;
        }

    </style>
</head>
<body>
    <!-- 查询表单 -->
    <form class="layui-form">
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">RFQ单号</label>
                <div class="layui-input-inline">
                    <input type="text" name="RFQNo" placeholder="请输入RFQ单号" autocomplete="off" class="layui-input">
                </div>
            </div>
            
            <div class="layui-inline">
                <button class="layui-btn" lay-submit lay-filter="searchForm">查询</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </div>
    </form>

    <!-- 数据表格 -->
    <div class="table-container">
        <table id="rfqTable" lay-filter="rfqTable"></table>
    </div>
    
   
       <script src="../lib/layui-v2.5.5/layui.js" charset="utf-8"></script>
    

    <script>
        layui.use(['table', 'form', 'layer', 'jquery'], function () {
        var table = layui.table;
        var form = layui.form;
        var layer = layui.layer;
        var $ = layui.$;
            // 使用全局变量记录当前打开的RFQNo，防止重复打开
            var currentOpenRFQNo = null;
            
            // 获取文件名函数
            window.getFileName = function (filePath) {
                if (!filePath) return '';
                var parts = filePath.split('\\');
                return parts[parts.length - 1];
            }

            var tableIns = table.render({
                elem: '#rfqTable',
                id: 'rfqTable',  // 添加表格ID
                height: 'full-200',
                data:[],
                cols: [[
                    {
                        field: 'RFQNO',
                        title: 'RFQ单号',
                        width: 250,
                        templet: function (d) {
                            return '<span class="rfq-link" style="color: #1E9FFF; cursor: pointer; font-weight: 500;">' + d.RFQNO + '</span>';
                        }
                    },
                    {
                        field: 'CREATETIME',
                        title: '创建日期',
                        width: 120
                    },
                    {
                        field: 'FLOWSTATUS',
                        title: '状态',
                        width: 150,
                        templet: function (d) {
                            var statusMap = {
                                '1': '<span style="color: #FFB800">待Manager签核</span>',
                                '2': '<span style="color: #FFB800">待HOD签核</span>',
                                '3': '<span style="color: #009688">签核完成</span>',
                                default: '<span style="color: #FF5722">未开始签核</span>'
                            };
                            return statusMap[d.FLOWSTATUS] || statusMap.default;
                        }
                    }
                ]],
                limit: 10000,
                text: {
                    none: '暂无相关数据'
                },
                even: true,
                size: 'lg'
            });

            // 监听单元格点击事件
            table.on('tool(rfqTable)', function (obj) {
                var data = obj.data;
                if (obj.event === 'click') {
                    showFormDetails(data.rfqno);
                }
            });

            // 自定义点击RFQNo列 
            $(document).off('click', '.rfq-link').on('click', '.rfq-link', function () {
                var RFQNo = $(this).text();

                // 如果点击的是同一个RFQ，直接返回
                if (currentOpenRFQNo === RFQNo) {
                    return;
                }

                // 重置当前打开的RFQNo
                currentOpenRFQNo = RFQNo;

                // 显示报价详情弹窗
                showFormDetails(RFQNo);
            });


            // 显示签核单详情弹窗
            function showFormDetails(rfqno) {
                // 参数验证
                if (!rfqno) {
                    layer.msg('无效的RFQ单号');
                    return;
                }

                // 先关闭所有已存在的弹窗
                layer.closeAll('page');

                // 修复变量名拼写错误
                console.log('Opening flow details for:', rfqno);

                // 定义加载索引变量
                let loadingIndex;
                // 打开新的弹窗
                layer.open({
                    type: 1,
                    title: 'RFQ签核流程单 - ' + rfqno,
                    area: ['90%', '90%'], // 增加弹窗大小
                    skin: 'flow-detail-popup',
                    content: '<div style="width: 100%;"><div class="loading-placeholder" style="text-align: center; padding: 20px;">数据加载中...</div></div>',
                    success: function (layero, index) {
                        // 显示加载中
                        loadingIndex = layer.load(2, {
                            shade: [0.2, '#000']
                        });

                        // 立即加载数据，不需要setTimeout
                        loadFormDetails(rfqno, function (success) {
                            // 关闭加载提示
                            layer.close(loadingIndex);

                            if (!success) {
                                // 如果加载失败，关闭弹窗
                                layer.close(index);
                            }
                        });

                        // 优化弹窗内部滚动
                        $(layero).find('.layui-layer-content').css({
                            'padding': '0',
                            'overflow-x': 'hidden',
                            'overflow-y': 'auto'
                        });

                        // 给弹窗添加最小宽度
                        $(layero).css({
                            'min-width': '1200px'
                        });
                    },
                    end: function () {
                        // 重置当前打开的RFQNo
                        currentOpenRFQNo = null;

                        // 确保关闭所有loading层
                        layer.closeAll('loading');

                        // 刷新主表格数据
                        loadData({});

                        
                    }
                });
            }

            // 加载详细表单信息
            function loadFormDetails(rfqno, callback) {
                // 获取表单详情数据
                $.ajax({
                    url: '../ashx/FlowControl.ashx/GetFlowDetail',
                    type: 'GET',
                    data: { rfqno: rfqno },
                    success: function (res) {
                        if (res.code === 0) {
                            // 构建表单内容
                            var content = `
                    <div class="flow-detail-container layui-form" lay-filter="flowDetailForm" style="margin: 0 -15px;">
                        <!-- 项目基本信息 -->
                        <div class="layui-card">
                            <div class="layui-card-header">项目基本信息</div>
                            <div class="layui-card-body">
                                <div class="layui-form-item">
                                    <div class="layui-inline">
                                        <label class="layui-form-label">客户编号</label>
                                        <div class="layui-input-inline">
                                            <input type="text" name="customerCode" class="layui-input" readonly value="${res.data.customerCode || ''}">
                                        </div>
                                    </div>
                                    <div class="layui-inline">
                                        <label class="layui-form-label">客户名称</label>
                                        <div class="layui-input-inline">
                                            <input type="text" name="customerName" class="layui-input" readonly value="${res.data.customerName || ''}">
                                        </div>
                                    </div>
                                    <div class="layui-inline">
                                        <label class="layui-form-label">项目应用</label>
                                        <div class="layui-input-inline">
                                            <input type="text" name="projectApplication" class="layui-input" readonly value="${res.data.projectApplication || ''}">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <div class="layui-inline">
                                        <label class="layui-form-label">设计地</label>
                                        <div class="layui-input-inline">
                                            <input type="text" name="designLocation" class="layui-input" readonly value="${res.data.designLocation || ''}">
                                        </div>
                                    </div>
                                    <div class="layui-inline">
                                        <label class="layui-form-label">汇率</label>
                                        <div class="layui-input-inline">
                                            <input type="text" name="rate" class="layui-input" readonly value="${res.data.rate || ''}">
                                        </div>
                                    </div>
                                    <div class="layui-inline">
                                        <label class="layui-form-label">成本下降比例</label>
                                        <div class="layui-input-inline">
                                            <input type="text" name="costReduction" class="layui-input" readonly value="${res.data.costReduction || ''}%">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <div class="layui-inline">
                                        <label class="layui-form-label">报价币别</label>
                                        <div class="layui-input-inline">
                                            <input type="text" name="currency" class="layui-input" readonly value="${res.data.currency || ''}">
                                        </div>
                                    </div>
                                    <div class="layui-inline">
                                        <label class="layui-form-label">创建人</label>
                                        <div class="layui-input-inline">
                                            <input type="text" name="createdBy" class="layui-input" readonly value="${res.data.createdBy || ''}">
                                        </div>
                                    </div>
                                    <div class="layui-inline">
                                        <label class="layui-form-label">创建时间</label>
                                        <div class="layui-input-inline">
                                            <input type="text" name="createdTime" class="layui-input" readonly value="${res.data.createdTime || ''}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- BOM详细信息表格 -->
                        <div class="layui-card">
                            <div class="layui-card-header">BOM详细信息</div>
                            <div class="layui-card-body">
                                <table id="bomDetailTable" lay-filter="bomDetailTable"></table>
                            </div>
                        </div>
                        <!-- 签核流程详情 -->
                        <div class="layui-card">
                            <div class="layui-card-header">签核流程详情</div>
                            <div class="layui-card-body">
                                <table class="layui-table" lay-skin="line">
                                    <thead>
                                        <tr>
                                            <th>签核节点</th>
                                            <th>签核人</th>
                                            <th>签核状态</th>
                                            <th>签核时间</th>
                                            <th>驳回原因</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${res.data.approvalFlow ? res.data.approvalFlow.map(flow => `
                                            <tr>
                                                <td>${flow.NODENAME || '-'}</td>
                                                <td>${flow.NODEASSIGNNAME || '-'}</td>
                                                <td>
                                                    <span class="status-badge ${getStatusText(flow.AUDITSTATUS)}">
                                                        ${getStatusText(flow.AUDITSTATUS)}
                                                    </span>
                                                </td>
                                                <td>${flow.ASSIGNDATE || '-'}</td>
                                                <td>${flow.REMARKS || '-'}</td>
                                            </tr>
                                        `).join('') : '<tr><td colspan="5">暂无签核记录</td></tr>'}
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 审批按钮区域 -->
                        ${res.data.hasPermission ? `
<div class="layui-card">
    <div class="layui-card-body">
        <div class="layui-form-item" style="margin: 0; padding: 10px 0; text-align: center;">
            <button type="button" class="layui-btn layui-btn-normal" lay-submit lay-filter="approveSubmit">审批通过</button>
            <button type="button" class="layui-btn layui-btn-danger" lay-submit lay-filter="rejectSubmit">驳回</button>
        </div>
    </div>
</div>
                        ` : ''}
                    </div>
                `;
                            console.log(res.data.approvalFlow);
                            // 更新弹窗内容
                            $('.flow-detail-popup .layui-layer-content').html(content);

                            // 初始化BOM表格
                            setTimeout(function () {
                                initBomTable(res.data.bomData);
                            }, 100);

                            // 移除加载层
                            layer.closeAll('loading');

                            // 重新渲染表单
                            form.render();

                            // 绑定审批事件
                            if (res.data.hasPermission) {
                                bindApprovalEvents(rfqno);
                            }
                            // 成功回调
                            callback && callback(true);
                        } else {
                            layer.msg('加载数据失败：' + res.msg);
                            callback && callback(false);
                        }
                    },
                    error: function () {
                        layer.msg('请求失败，请检查网络连接');

                        layer.closeAll('loading');
                        callback && callback(false);
                    }
                });
            }

        //初始化bom资料
        function initBomTable(data) {
            if (!data || data.length === 0) {
                layer.msg('暂无 BOM 数据');
                return;
            }

            // 从第一行数据中提取所有字段作为列定义
            var cols = Object.keys(data[0])
                .filter(function (field) {
                    // 过滤掉 Id、id、ID 等形式的列
                    return !field.toLowerCase().match(/^id$/);
                })
                .map(function (field) {
                    // 检查是否为文件上传字段
                    if (field.toLowerCase().startsWith('file_upload')) {
                        return {
                            field: field,
                            title: '附件' + field.slice(-1),
                            minWidth: 120,
                            align: 'center',
                            templet: function (d) {
                                if (d[field]) {
                                    var fileName = getFileName(d[field]); // 获取文件名
                                    return '<button class="layui-btn layui-btn-xs download-btn" ' +
                                        'lay-event="download_' + field + '" ' +
                                        'data-file="' + d[field] + '">' +
                                        '<i class="layui-icon layui-icon-download-circle"></i> ' +
                                        fileName +
                                        '</button>';
                                }
                                return '';
                            }
                        };
                    }
                    return {
                        field: field,
                        title: field,
                        minWidth: 120,
                        align: 'center',
                        templet: function (d) {
                            if (typeof d[field] === 'number') {
                                if (field.toLowerCase().includes('rate') ||
                                    field.toLowerCase().includes('percent') ||
                                    field.toLowerCase().includes('ratio')) {
                                    return (d[field] * 100).toFixed(2) + '%';
                                }
                                if (field.toLowerCase().includes('price') ||
                                    field.toLowerCase().includes('cost') ||
                                    field.toLowerCase().includes('amount')) {
                                    return d[field].toFixed(2);
                                }
                                return d[field].toString();
                            }
                            return d[field] || '';
                        }
                    };
                });

            // 渲染表格
            table.render({
                elem: '#bomDetailTable',
                id: 'bomDetailTable',  // 添加表格ID
                data:data,
                cols: [cols],
                limit: 1000,
                height: '300px',
                even: true,
                size: 'sm',
                text: {
                    none: '暂无相关数据'
                },
                done: function () {
                    // 自适应列宽
                    $('.layui-table-body').css('overflow-x', 'auto');

                    

                }
            });

                // 监听表格中的按钮点击事件
                table.on('tool(bomDetailTable)', function (obj) {
                    var data = obj.data;
                    var event = obj.event;

                    // 处理下载事件
                    if (event.startsWith('download_')) {
                        var field = event.split('download_')[1];
                        var filePath = data[field];
                        if (filePath) {
                            downloadFile(filePath);
                        } else {
                            layer.msg('文件路径不存在');
                        }
                    }
                });
        }


        // 监听查询表单提交
        form.on('submit(searchForm)', function(data){
            loadData(data.field);
            layer.closeAll('loading');
            return false;
        });

        // 绑定审批事件
        function bindApprovalEvents(rfqno) {
            // 审批通过
            form.on('submit(approveSubmit)', function () {
                layer.confirm('确认审批通过该签核单？', function (index) {
                    submitApproval(rfqno, 'approve');
                    layer.close(index);
                });
                return false;
            });

            // 驳回
            form.on('submit(rejectSubmit)', function () {
                layer.prompt({
                    formType: 2,
                    title: '请输入驳回原因',
                    area: ['400px', '200px']
                }, function (value, index) {
                    submitApproval(rfqno, 'reject', value);
                    layer.close(index);
                });
                return false;
            });
        }

        // 提交审批结果
        function submitApproval(rfqno, action, reason) {
            var data = {
                rfqno: rfqno,
                action: action,
                reason: reason || ''
            };

            layer.load();
            $.ajax({
                url: '../ashx/FlowControl.ashx/SubmitApproval',
                type: 'POST',
                data:data,
                success: function (res) {
                    layer.closeAll('loading');
                    if (res.code === 0) {
                        layer.msg('操作成功');
                        // 关闭弹窗
                        layer.closeAll('page');
                        // 刷新主表格
                        loadData({});
                    } else {
                        layer.msg('操作失败：' + res.msg);
                    }
                },
                error: function () {
                    layer.closeAll('loading');
                    layer.msg('请求失败，请检查网络连接');
                }
            });
        }

        
        // 获取文件名函数
        function getFileName(filePath) {
            if (!filePath) return '';
            var parts = filePath.split('\\');
            return parts[parts.length - 1];
        }

        function getStatusText(status) {
            switch (status) {
                case 1:
                    return '已通过';
                case 2:
                    return '已驳回';
                case 0:
                    return '待审批';
                default:
                    return '未知状态';
            }
        }

        // 加载数据函数
        function loadData(params) {
            // 实际使用时替换为真实的接口调用
            var url = '../ashx/FlowControl.ashx/QuerySelfFlow';
            layer.load();
            $.ajax({
                url: url,
                type: 'GET',
                data: params,
                dataType: 'json',  // 添加这行，指定返回数据类型为 json
                success: function (res) {
                    //console.log(response);
                    layer.closeAll('loading');
                    //var res = JSON.parse(response);
                    console.log(res.data);
                    console.log(res.code);
                    if (res.code === 0) {
                        // 修正这里的重载方式
                        tableIns.reload({
                            data: res.data
                            
                        });
                    } else {
                        layer.msg('加载数据失败：' + res.msg);
                    }
                },
                error: function (xhr, status, error) {
                    layer.closeAll('loading');
                    layer.msg('请求失败，请检查网络连接');
                    console.error('Ajax error:', status, error);
                    console.log('Response Text:', xhr.responseText);  // 查看原始响应
                }
            });
        }

        
            // 下载文件函数
            function downloadFile(filePath) {
                // 实际使用时替换为真实的下载逻辑
                window.open('../ashx/QuoteControl.ashx?action=DownLoadFile&&filePath=' + encodeURIComponent(filePath));
            }
        

        // 初始加载数据
        loadData({});
    });
    </script>
</body>

