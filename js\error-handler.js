/**
 * RFQ系统统一错误处理模块
 * 提供统一的错误显示和处理功能
 * 作者：RFQ系统开发团队
 * 创建时间：2024年
 */

(function(window) {
    'use strict';

    // 错误处理器对象
    var ErrorHandler = {
        
        /**
         * 显示详细错误信息的弹窗
         * @param {string} operation - 操作类型/名称
         * @param {string} error - 错误信息
         * @param {string} details - 详细信息（可选）
         * @param {object} options - 配置选项（可选）
         */
        showErrorDetails: function(operation, error, details, options) {
            // 默认配置
            var config = {
                title: '错误详情',
                width: '600px',
                height: '500px',
                showCopyButton: true,
                showContactInfo: true
            };
            
            // 合并用户配置
            if (options) {
                for (var key in options) {
                    if (options.hasOwnProperty(key)) {
                        config[key] = options[key];
                    }
                }
            }

            var errorHtml = '<div style="padding: 20px; max-height: 400px; overflow-y: auto;">';
            errorHtml += '<h3 style="color: #FF5722; margin-bottom: 15px;">';
            errorHtml += '<i class="layui-icon layui-icon-close-fill"></i> 操作失败</h3>';
            errorHtml += '<div style="margin-bottom: 10px;"><strong>操作类型：</strong>' + this._escapeHtml(operation) + '</div>';
            errorHtml += '<div style="margin-bottom: 10px;"><strong>错误信息：</strong>' + this._escapeHtml(error) + '</div>';
            errorHtml += '<div style="margin-bottom: 10px;"><strong>发生时间：</strong>' + new Date().toLocaleString() + '</div>';
            
            if (details) {
                errorHtml += '<div style="margin-bottom: 10px;"><strong>详细信息：</strong></div>';
                errorHtml += '<div style="background: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; white-space: pre-wrap; max-height: 200px; overflow-y: auto;">';
                errorHtml += this._escapeHtml(details) + '</div>';
            }
            
            if (config.showContactInfo) {
                errorHtml += '<div style="margin-top: 15px; color: #666; font-size: 12px;">';
                errorHtml += '如果问题持续存在，请联系系统管理员并提供上述错误信息。</div>';
            }
            errorHtml += '</div>';
            
            var buttons = ['关闭'];
            var buttonCallbacks = {};
            
            if (config.showCopyButton) {
                buttons.unshift('复制错误信息');
                buttonCallbacks.yes = function(index, layero) {
                    ErrorHandler._copyErrorInfo(operation, error, details);
                };
            }
            
            // 检查layui是否可用
            if (typeof layui !== 'undefined' && layui.layer) {
                layui.layer.open({
                    type: 1,
                    title: config.title,
                    content: errorHtml,
                    area: [config.width, config.height],
                    btn: buttons,
                    yes: buttonCallbacks.yes
                });
            } else {
                // 降级处理：使用原生alert
                var alertMsg = '操作失败\n\n';
                alertMsg += '操作类型：' + operation + '\n';
                alertMsg += '错误信息：' + error + '\n';
                alertMsg += '发生时间：' + new Date().toLocaleString();
                if (details) {
                    alertMsg += '\n\n详细信息：\n' + details;
                }
                alert(alertMsg);
            }
        },

        /**
         * 统一的AJAX错误处理函数
         * @param {object} xhr - XMLHttpRequest对象
         * @param {string} status - 状态文本
         * @param {string} error - 错误信息
         * @param {string} operation - 操作名称
         */
        handleAjaxError: function(xhr, status, error, operation) {
            var errorMsg = '请求失败';
            var details = '';
            
            if (xhr.responseText) {
                try {
                    var response = JSON.parse(xhr.responseText);
                    if (response.msg) {
                        errorMsg = response.msg;
                    } else if (response.message) {
                        errorMsg = response.message;
                    }
                    details = 'HTTP状态: ' + xhr.status + '\n响应内容: ' + xhr.responseText;
                } catch (e) {
                    errorMsg = xhr.responseText.length > 200 ? 
                        xhr.responseText.substring(0, 200) + '...' : 
                        xhr.responseText;
                    details = 'HTTP状态: ' + xhr.status + '\n原始响应: ' + xhr.responseText;
                }
            } else {
                errorMsg = error || '网络连接失败';
                details = 'HTTP状态: ' + xhr.status + '\n状态文本: ' + xhr.statusText + '\n错误类型: ' + status;
            }
            
            this.showErrorDetails(operation, errorMsg, details);
        },

        /**
         * 处理JSON解析错误
         * @param {string} operation - 操作名称
         * @param {string} responseText - 服务器响应文本
         * @param {Error} parseError - 解析错误对象
         */
        handleParseError: function(operation, responseText, parseError) {
            var errorMsg = '数据解析失败';
            var details = '服务器返回的数据格式不正确\n\n';
            details += '响应内容: ' + (responseText.length > 500 ? responseText.substring(0, 500) + '...' : responseText);
            details += '\n\n解析错误: ' + parseError.message;
            
            this.showErrorDetails(operation, errorMsg, details);
        },

        /**
         * 显示简单的错误消息（使用layui.layer.msg）
         * @param {string} message - 错误消息
         * @param {object} options - layui.layer.msg的选项
         */
        showSimpleError: function(message, options) {
            var defaultOptions = {
                icon: 2,
                time: 3000
            };
            
            if (options) {
                for (var key in options) {
                    if (options.hasOwnProperty(key)) {
                        defaultOptions[key] = options[key];
                    }
                }
            }
            
            if (typeof layui !== 'undefined' && layui.layer) {
                layui.layer.msg(message, defaultOptions);
            } else {
                alert(message);
            }
        },

        /**
         * 显示成功消息
         * @param {string} message - 成功消息
         * @param {object} options - layui.layer.msg的选项
         */
        showSuccess: function(message, options) {
            var defaultOptions = {
                icon: 1,
                time: 2000
            };
            
            if (options) {
                for (var key in options) {
                    if (options.hasOwnProperty(key)) {
                        defaultOptions[key] = options[key];
                    }
                }
            }
            
            if (typeof layui !== 'undefined' && layui.layer) {
                layui.layer.msg(message, defaultOptions);
            } else {
                alert(message);
            }
        },

        /**
         * 复制错误信息到剪贴板
         * @private
         */
        _copyErrorInfo: function(operation, error, details) {
            var errorText = '操作类型：' + operation + '\n';
            errorText += '错误信息：' + error + '\n';
            errorText += '发生时间：' + new Date().toLocaleString();
            if (details) {
                errorText += '\n\n详细信息：\n' + details;
            }
            
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(errorText).then(function() {
                    if (typeof layui !== 'undefined' && layui.layer) {
                        layui.layer.msg('错误信息已复制到剪贴板', {icon: 1});
                    }
                }).catch(function() {
                    ErrorHandler._fallbackCopy(errorText);
                });
            } else {
                this._fallbackCopy(errorText);
            }
        },

        /**
         * 降级复制方法
         * @private
         */
        _fallbackCopy: function(text) {
            var textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.opacity = '0';
            document.body.appendChild(textArea);
            textArea.select();
            
            try {
                document.execCommand('copy');
                if (typeof layui !== 'undefined' && layui.layer) {
                    layui.layer.msg('错误信息已复制到剪贴板', {icon: 1});
                }
            } catch (err) {
                if (typeof layui !== 'undefined' && layui.layer) {
                    layui.layer.msg('复制失败，请手动复制错误信息', {icon: 2});
                }
            }
            
            document.body.removeChild(textArea);
        },

        /**
         * HTML转义，防止XSS攻击
         * @private
         */
        _escapeHtml: function(text) {
            if (typeof text !== 'string') {
                return text;
            }
            var map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };
            return text.replace(/[&<>"']/g, function(m) { return map[m]; });
        }
    };

    // 将ErrorHandler暴露到全局
    window.ErrorHandler = ErrorHandler;

    // 如果支持模块化，也支持模块化导出
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = ErrorHandler;
    }

})(window);
