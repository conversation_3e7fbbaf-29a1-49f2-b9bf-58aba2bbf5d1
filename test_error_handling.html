<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>错误处理测试页面</title>
    <link rel="stylesheet" href="lib/layui-v2.9.21/css/layui.css" media="all">
</head>
<body>
    <div style="padding: 20px;">
        <h2>错误处理功能测试</h2>
        <div class="layui-btn-group">
            <button class="layui-btn" id="testSuccess">测试成功响应</button>
            <button class="layui-btn layui-btn-warm" id="testError">测试错误响应</button>
            <button class="layui-btn layui-btn-danger" id="testNetworkError">测试网络错误</button>
            <button class="layui-btn layui-btn-normal" id="testParseError">测试解析错误</button>
        </div>
        
        <div style="margin-top: 20px;">
            <h3>测试说明：</h3>
            <ul>
                <li>测试成功响应：模拟正常的成功响应</li>
                <li>测试错误响应：模拟服务器返回的错误信息</li>
                <li>测试网络错误：模拟网络连接失败</li>
                <li>测试解析错误：模拟JSON解析失败</li>
            </ul>
        </div>
    </div>

    <script src="lib/layui-v2.9.21/layui.js"></script>
    <script src="js/error-handler.js"></script>
    <script>
        layui.use(['layer', 'jquery'], function() {
            var layer = layui.layer;
            var $ = layui.jquery;

            // 测试按钮事件
            $('#testSuccess').click(function() {
                ErrorHandler.showSuccess('成功响应测试');
            });

            $('#testError').click(function() {
                ErrorHandler.showErrorDetails('测试操作', '这是一个模拟的错误信息', '详细错误信息：\n- 错误代码：TEST_ERROR_001\n- 错误位置：测试函数\n- 建议解决方案：这只是一个测试');
            });

            $('#testNetworkError').click(function() {
                $.ajax({
                    url: '/nonexistent-endpoint',
                    type: 'POST',
                    success: function(data) {
                        ErrorHandler.showSimpleError('不应该到达这里');
                    },
                    error: function(xhr, status, error) {
                        ErrorHandler.handleAjaxError(xhr, status, error, '网络错误测试');
                    }
                });
            });

            $('#testParseError').click(function() {
                var invalidJson = '{"invalid": json}';
                try {
                    JSON.parse(invalidJson);
                } catch (e) {
                    ErrorHandler.handleParseError('JSON解析测试', invalidJson, e);
                }
            });
        });
    </script>
</body>
</html>
