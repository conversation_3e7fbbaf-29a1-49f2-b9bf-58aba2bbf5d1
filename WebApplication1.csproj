﻿<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="packages\EntityFramework.6.4.4\build\EntityFramework.props" Condition="Exists('packages\EntityFramework.6.4.4\build\EntityFramework.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{849435B5-B360-4A34-91C6-FFCE6FEA6A46}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>WebApplication1</RootNamespace>
    <AssemblyName>WebApplication1</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort>44332</IISExpressSSLPort>
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Antlr3.Runtime">
      <HintPath>packages\Antlr.*******\lib\Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="AspNet.ScriptManager.bootstrap">
      <HintPath>packages\AspNet.ScriptManager.bootstrap.3.4.1\lib\net40\AspNet.ScriptManager.bootstrap.dll</HintPath>
    </Reference>
    <Reference Include="AspNet.ScriptManager.jQuery">
      <HintPath>packages\AspNet.ScriptManager.jQuery.3.4.1\lib\net40\AspNet.ScriptManager.jQuery.dll</HintPath>
    </Reference>
    <Reference Include="Aspose.Cells, Version=********, Culture=neutral, PublicKeyToken=716fcc553a201e56, processorArchitecture=MSIL">
      <HintPath>packages\Aspose.Cells.24.7.0\lib\net40\Aspose.Cells.dll</HintPath>
    </Reference>
    <Reference Include="CsvHelper, Version=*******, Culture=neutral, PublicKeyToken=8c4959082be5c823, processorArchitecture=MSIL">
      <HintPath>packages\CsvHelper.********\lib\net40\CsvHelper.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>packages\EntityFramework.6.4.4\lib\net40\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>packages\EntityFramework.6.4.4\lib\net40\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="EPPlus, Version=7.1.3.0, Culture=neutral, PublicKeyToken=ea159fdaa78159a1, processorArchitecture=MSIL">
      <HintPath>packages\EPPlus.7.1.3\lib\net35\EPPlus.dll</HintPath>
    </Reference>
    <Reference Include="EPPlus.Interfaces, Version=6.1.1.0, Culture=neutral, PublicKeyToken=a694d7f3b0907a61, processorArchitecture=MSIL">
      <HintPath>packages\EPPlus.Interfaces.6.1.1\lib\net35\EPPlus.Interfaces.dll</HintPath>
    </Reference>
    <Reference Include="EPPlus.System.Drawing, Version=6.1.1.0, Culture=neutral, PublicKeyToken=2308d35469c9bac0, processorArchitecture=MSIL">
      <HintPath>packages\EPPlus.System.Drawing.6.1.1\lib\net35\EPPlus.System.Drawing.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.FriendlyUrls">
      <HintPath>packages\Microsoft.AspNet.FriendlyUrls.Core.1.0.2\lib\net40\Microsoft.AspNet.FriendlyUrls.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Web.Optimization.WebForms">
      <HintPath>packages\Microsoft.AspNet.Web.Optimization.WebForms.1.1.3\lib\net45\Microsoft.AspNet.Web.Optimization.WebForms.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.VisualBasic" />
    <Reference Include="Microsoft.Web.Infrastructure">
      <HintPath>packages\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>packages\Newtonsoft.Json.13.0.3\lib\net40\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Optimization">
      <HintPath>packages\Microsoft.AspNet.Web.Optimization.1.1.3\lib\net40\System.Web.Optimization.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="WebGrease">
      <HintPath>packages\WebGrease.1.6.0\lib\WebGrease.dll</HintPath>
    </Reference>
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="apijs\init.js" />
    <Content Include="apijs\menus.js" />
    <Content Include="apijs\table.js" />
    <Content Include="apijs\tableSelect.js" />
    <Content Include="apijs\tree_table.js" />
    <Content Include="AssignFlow\FlowApply.aspx" />
    <Content Include="AssignFlow\FlowAssign.aspx" />
    <Content Include="AssignFlow\FlowAssignDetail.aspx" />
    <Content Include="Content\bootstrap-theme.css.map" />
    <Content Include="Content\bootstrap-theme.min.css.map" />
    <Content Include="Content\bootstrap.css.map" />
    <Content Include="Content\bootstrap.min.css.map" />
    <Content Include="css\layuimini.css" />
    <Content Include="css\public.css" />
    <Content Include="css\themes\default.css" />
    <Content Include="DataManager\AddCsvConfig.aspx" />
    <Content Include="DataManager\EditCsvConfig.aspx" />
    <Content Include="DataManager\FieldManage.aspx" />
    <Content Include="DataManager\GroupConfig.aspx" />
    <Content Include="DataManager\QueryFile.aspx" />
    <Content Include="DataManager\QueryHistoryRFQ.aspx" />
    <Content Include="DataManager\QueryQuote.aspx" />
    <Content Include="DataManager\RateConfig.aspx" />
    <Content Include="DataManager\SupplierManage.aspx" />
    <Content Include="DataManager\TemplateManage.aspx" />
    <Content Include="DataManager\TemplateManage2.aspx" />
    <Content Include="favicon.ico" />
    <Content Include="Flow\AddProject.aspx" />
    <Content Include="Flow\ConfirmPart.aspx" />
    <Content Include="Flow\CreateRfq.aspx" />
    <Content Include="Flow\CreateRfq2.aspx" />
    <Content Include="Flow\CreateRfq.html" />
    <Content Include="Flow\CustomerTrans.aspx" />
    <Content Include="Flow\EditProject.aspx" />
    <Content Include="Flow\EditProject.html" />
    <Content Include="DataManager\QueryHistoryQuote.aspx" />
    <Content Include="Flow\SendQuote.aspx" />
    <Content Include="fonts\glyphicons-halflings-regular.eot" />
    <Content Include="fonts\glyphicons-halflings-regular.ttf" />
    <Content Include="fonts\glyphicons-halflings-regular.woff" />
    <Content Include="fonts\glyphicons-halflings-regular.woff2" />
    <Content Include="Form.html" />
    <Content Include="Global.asax" />
    <Content Include="images\bg.jpg" />
    <Content Include="images\captcha.jpg" />
    <Content Include="images\donate_qrcode.png" />
    <Content Include="images\error\ic_404.png" />
    <Content Include="images\favicon.ico" />
    <Content Include="images\group.png" />
    <Content Include="images\home.png" />
    <Content Include="images\icon-login.png" />
    <Content Include="images\loginbg.png" />
    <Content Include="images\logo.png" />
    <Content Include="ImportBom.aspx" />
    <Content Include="index.aspx" />
    <Content Include="js\guid.js" />
    <Content Include="js\lay-config.js" />
    <Content Include="js\lay-module\cookie\jquery.cookie.js" />
    <Content Include="js\lay-module\cookie\jquery.cookie.min.js" />
    <Content Include="js\lay-module\echarts\echarts.js" />
    <Content Include="js\lay-module\echarts\echartsTheme.js" />
    <Content Include="js\lay-module\iconPicker\iconPickerFa.js" />
    <Content Include="js\lay-module\layarea\layarea.js" />
    <Content Include="js\lay-module\layuimini\miniAdmin.js" />
    <Content Include="js\lay-module\layuimini\miniMenu.js" />
    <Content Include="js\lay-module\layuimini\miniPage.js" />
    <Content Include="js\lay-module\layuimini\miniTab.js" />
    <Content Include="js\lay-module\layuimini\miniTheme.js" />
    <Content Include="js\lay-module\layuimini\miniTongji.js" />
    <Content Include="js\lay-module\step-lay\step.css" />
    <Content Include="js\lay-module\step-lay\step.js" />
    <Content Include="js\lay-module\tableSelect\tableSelect.js" />
    <Content Include="js\lay-module\treetable-lay\treetable.css" />
    <Content Include="js\lay-module\treetable-lay\treetable.js" />
    <Content Include="js\lay-module\wangEditor\wangEditor.css" />
    <Content Include="js\lay-module\wangEditor\wangEditor.js" />
    <Content Include="js\lay-module\wangEditor\wangEditor.min.css" />
    <Content Include="js\lay-module\wangEditor\wangEditor.min.js" />
    <Content Include="js\navigate.js" />
    <Content Include="lib\font-awesome-4.7.0\css\font-awesome.css" />
    <Content Include="lib\font-awesome-4.7.0\css\font-awesome.min.css" />
    <Content Include="lib\font-awesome-4.7.0\fonts\fontawesome-webfont.svg" />
    <Content Include="lib\font-awesome-4.7.0\HELP-US-OUT.txt" />
    <Content Include="lib\jq-module\jquery.particleground.min.js" />
    <Content Include="lib\jq-module\paigusu.min.js" />
    <Content Include="lib\jq-module\zyupload\zyupload-1.0.0.min.css" />
    <Content Include="lib\jq-module\zyupload\zyupload-1.0.0.min.js" />
    <Content Include="lib\jquery-3.4.1\jquery-3.4.1.min.js" />
    <Content Include="lib\layui-v2.5.5\css\layui.css" />
    <Content Include="lib\layui-v2.5.5\css\layui.mobile.css" />
    <Content Include="lib\layui-v2.5.5\css\modules\code.css" />
    <Content Include="lib\layui-v2.5.5\css\modules\laydate\default\laydate.css" />
    <Content Include="lib\layui-v2.5.5\css\modules\layer\default\icon-ext.png" />
    <Content Include="lib\layui-v2.5.5\css\modules\layer\default\icon.png" />
    <Content Include="lib\layui-v2.5.5\css\modules\layer\default\layer.css" />
    <Content Include="lib\layui-v2.5.5\css\modules\layer\default\loading-0.gif" />
    <Content Include="lib\layui-v2.5.5\css\modules\layer\default\loading-1.gif" />
    <Content Include="lib\layui-v2.5.5\css\modules\layer\default\loading-2.gif" />
    <Content Include="lib\layui-v2.5.5\font\iconfont.svg" />
    <Content Include="lib\layui-v2.5.5\images\face\0.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\1.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\10.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\11.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\12.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\13.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\14.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\15.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\16.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\17.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\18.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\19.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\2.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\20.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\21.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\22.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\23.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\24.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\25.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\26.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\27.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\28.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\29.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\3.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\30.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\31.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\32.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\33.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\34.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\35.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\36.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\37.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\38.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\39.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\4.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\40.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\41.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\42.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\43.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\44.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\45.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\46.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\47.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\48.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\49.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\5.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\50.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\51.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\52.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\53.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\54.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\55.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\56.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\57.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\58.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\59.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\6.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\60.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\61.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\62.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\63.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\64.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\65.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\66.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\67.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\68.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\69.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\7.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\70.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\71.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\8.gif" />
    <Content Include="lib\layui-v2.5.5\images\face\9.gif" />
    <Content Include="lib\layui-v2.5.5\layui.all.js" />
    <Content Include="lib\layui-v2.5.5\layui.js" />
    <Content Include="lib\layui-v2.5.5\lay\modules\carousel.js" />
    <Content Include="lib\layui-v2.5.5\lay\modules\code.js" />
    <Content Include="lib\layui-v2.5.5\lay\modules\colorpicker.js" />
    <Content Include="lib\layui-v2.5.5\lay\modules\dropdown.js" />
    <Content Include="lib\layui-v2.5.5\lay\modules\element.js" />
    <Content Include="lib\layui-v2.5.5\lay\modules\flow.js" />
    <Content Include="lib\layui-v2.5.5\lay\modules\form.js" />
    <Content Include="lib\layui-v2.5.5\lay\modules\jquery.js" />
    <Content Include="lib\layui-v2.5.5\lay\modules\laydate.js" />
    <Content Include="lib\layui-v2.5.5\lay\modules\layedit.js" />
    <Content Include="lib\layui-v2.5.5\lay\modules\layer.js" />
    <Content Include="lib\layui-v2.5.5\lay\modules\laypage.js" />
    <Content Include="lib\layui-v2.5.5\lay\modules\laytpl.js" />
    <Content Include="lib\layui-v2.5.5\lay\modules\mobile.js" />
    <Content Include="lib\layui-v2.5.5\lay\modules\rate.js" />
    <Content Include="lib\layui-v2.5.5\lay\modules\slider.js" />
    <Content Include="lib\layui-v2.5.5\lay\modules\table.js" />
    <Content Include="lib\layui-v2.5.5\lay\modules\transfer.js" />
    <Content Include="lib\layui-v2.5.5\lay\modules\tree.js" />
    <Content Include="lib\layui-v2.5.5\lay\modules\upload.js" />
    <Content Include="lib\layui-v2.5.5\lay\modules\util.js" />
    <Content Include="lib\layui-v2.9.21\css\layui.css" />
    <Content Include="lib\layui-v2.9.21\font\iconfont.svg" />
    <Content Include="lib\layui-v2.9.21\layui.js" />
    <Content Include="Login.aspx" />
    <Content Include="main.aspx" />
    <Content Include="Scripts\jquery-3.4.1.min.map" />
    <Content Include="Scripts\jquery-3.4.1.slim.min.map" />
    <Content Include="Bundle.config" />
    <Content Include="api\clear.json" />
    <Content Include="api\init.json" />
    <Content Include="api\menus.json" />
    <Content Include="api\table.json" />
    <Content Include="api\tableSelect.json" />
    <Content Include="api\upload.json" />
    <Content Include="Supplier\AddQuote.aspx" />
    <Content Include="Supplier\QuoteDetail.aspx" />
    <Content Include="SystemManager\AddUser.aspx" />
    <Content Include="SystemManager\ChangePassWord.aspx" />
    <Content Include="SystemManager\EditUser.aspx" />
    <Content Include="SystemManager\UserManage.aspx" />
    <Content Include="js\lay-module\wangEditor\fonts\w-e-icon.woff" />
    <Content Include="js\lay-module\wangEditor\wangEditor.min.js.map" />
    <Content Include="lib\font-awesome-4.7.0\fonts\fontawesome-webfont.eot" />
    <Content Include="lib\font-awesome-4.7.0\fonts\fontawesome-webfont.ttf" />
    <Content Include="lib\font-awesome-4.7.0\fonts\fontawesome-webfont.woff" />
    <Content Include="lib\font-awesome-4.7.0\fonts\fontawesome-webfont.woff2" />
    <Content Include="lib\font-awesome-4.7.0\fonts\FontAwesome.otf" />
    <Content Include="lib\font-awesome-4.7.0\less\animated.less" />
    <Content Include="lib\font-awesome-4.7.0\less\bordered-pulled.less" />
    <Content Include="lib\font-awesome-4.7.0\less\core.less" />
    <Content Include="lib\font-awesome-4.7.0\less\fixed-width.less" />
    <Content Include="lib\font-awesome-4.7.0\less\font-awesome.less" />
    <Content Include="lib\font-awesome-4.7.0\less\icons.less" />
    <Content Include="lib\font-awesome-4.7.0\less\larger.less" />
    <Content Include="lib\font-awesome-4.7.0\less\list.less" />
    <Content Include="lib\font-awesome-4.7.0\less\mixins.less" />
    <Content Include="lib\font-awesome-4.7.0\less\path.less" />
    <Content Include="lib\font-awesome-4.7.0\less\rotated-flipped.less" />
    <Content Include="lib\font-awesome-4.7.0\less\screen-reader.less" />
    <Content Include="lib\font-awesome-4.7.0\less\stacked.less" />
    <Content Include="lib\font-awesome-4.7.0\less\variables.less" />
    <Content Include="lib\font-awesome-4.7.0\scss\font-awesome.scss" />
    <Content Include="lib\font-awesome-4.7.0\scss\_animated.scss" />
    <Content Include="lib\font-awesome-4.7.0\scss\_bordered-pulled.scss" />
    <Content Include="lib\font-awesome-4.7.0\scss\_core.scss" />
    <Content Include="lib\font-awesome-4.7.0\scss\_fixed-width.scss" />
    <Content Include="lib\font-awesome-4.7.0\scss\_icons.scss" />
    <Content Include="lib\font-awesome-4.7.0\scss\_larger.scss" />
    <Content Include="lib\font-awesome-4.7.0\scss\_list.scss" />
    <Content Include="lib\font-awesome-4.7.0\scss\_mixins.scss" />
    <Content Include="lib\font-awesome-4.7.0\scss\_path.scss" />
    <Content Include="lib\font-awesome-4.7.0\scss\_rotated-flipped.scss" />
    <Content Include="lib\font-awesome-4.7.0\scss\_screen-reader.scss" />
    <Content Include="lib\font-awesome-4.7.0\scss\_stacked.scss" />
    <Content Include="lib\font-awesome-4.7.0\scss\_variables.scss" />
    <Content Include="lib\layui-v2.5.5\font\iconfont.eot" />
    <Content Include="lib\layui-v2.5.5\font\iconfont.ttf" />
    <Content Include="lib\layui-v2.5.5\font\iconfont.woff" />
    <Content Include="lib\layui-v2.5.5\font\iconfont.woff2" />
    <Content Include="ashx\HomeController.ashx" />
    <Content Include="ashx\LoginControl.ashx" />
    <Content Include="ashx\ProjectController.ashx" />
    <Content Include="ashx\UserController.ashx" />
    <Content Include="ashx\TemplateControl.ashx" />
    <Content Include="ashx\ImportCustomer.ashx" />
    <Content Include="ashx\ConfirmControl.ashx" />
    <Content Include="ashx\QuoteControl.ashx" />
    <Content Include="ashx\FlowControl.ashx" />
    <Content Include="ashx\ExchangeRate.ashx" />
    <Content Include="ashx\GroupControl.ashx" />
    <Content Include="ashx\HistoryControl.ashx" />
    <Content Include="ashx\SupplierControl.ashx" />
    <Content Include="lib\layui-v2.9.21\css\layui.css.map" />
    <Content Include="lib\layui-v2.9.21\font\iconfont.eot" />
    <Content Include="lib\layui-v2.9.21\font\iconfont.ttf" />
    <Content Include="lib\layui-v2.9.21\font\iconfont.woff" />
    <Content Include="lib\layui-v2.9.21\font\iconfont.woff2" />
    <Content Include="lib\layui-v2.9.21\layui.js.map" />
    <Content Include="api\tree_table.json" />
    <None Include="packages.config" />
    <Content Include="ashx\UploadExcel.ashx" />
    <None Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <Content Include="批量上传.xlsx" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Content\bootstrap-theme.css" />
    <Content Include="Content\bootstrap-theme.min.css" />
    <Content Include="Content\bootstrap.css" />
    <Content Include="Content\bootstrap.min.css" />
    <Content Include="Content\Site.css" />
    <Content Include="fonts\glyphicons-halflings-regular.svg" />
    <Content Include="Scripts\bootstrap.js" />
    <Content Include="Scripts\bootstrap.min.js" />
    <None Include="Scripts\jquery-3.4.1.intellisense.js" />
    <Content Include="Scripts\jquery-3.4.1.js" />
    <Content Include="Scripts\jquery-3.4.1.min.js" />
    <Content Include="Scripts\jquery-3.4.1.slim.js" />
    <Content Include="Scripts\jquery-3.4.1.slim.min.js" />
    <Content Include="Scripts\modernizr-2.8.3.js" />
    <Content Include="Scripts\WebForms\DetailsView.js" />
    <Content Include="Scripts\WebForms\Focus.js" />
    <Content Include="Scripts\WebForms\GridView.js" />
    <Content Include="Scripts\WebForms\Menu.js" />
    <Content Include="Scripts\WebForms\MenuStandards.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjax.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxApplicationServices.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxComponentModel.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxCore.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxGlobalization.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxHistory.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxNetwork.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxSerialization.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxTimer.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxWebForms.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxWebServices.js" />
    <Content Include="Scripts\WebForms\SmartNav.js" />
    <Content Include="Scripts\WebForms\TreeView.js" />
    <Content Include="Scripts\WebForms\WebForms.js" />
    <Content Include="Scripts\WebForms\WebParts.js" />
    <Content Include="Scripts\WebForms\WebUIValidation.js" />
    <Content Include="Web.config" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="App_Start\BundleConfig.cs" />
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="ashx\ConfirmControl.ashx.cs">
      <DependentUpon>ConfirmControl.ashx</DependentUpon>
    </Compile>
    <Compile Include="ashx\ExchangeRate.ashx.cs">
      <DependentUpon>ExchangeRate.ashx</DependentUpon>
    </Compile>
    <Compile Include="ashx\FlowControl.ashx.cs">
      <DependentUpon>FlowControl.ashx</DependentUpon>
    </Compile>
    <Compile Include="ashx\GroupControl.ashx.cs">
      <DependentUpon>GroupControl.ashx</DependentUpon>
    </Compile>
    <Compile Include="ashx\HistoryControl.ashx.cs">
      <DependentUpon>HistoryControl.ashx</DependentUpon>
    </Compile>
    <Compile Include="ashx\ImportCustomer.ashx.cs">
      <DependentUpon>ImportCustomer.ashx</DependentUpon>
    </Compile>
    <Compile Include="ashx\LoginControl.ashx.cs">
      <DependentUpon>LoginControl.ashx</DependentUpon>
    </Compile>
    <Compile Include="ashx\ProjectController.ashx.cs">
      <DependentUpon>ProjectController.ashx</DependentUpon>
    </Compile>
    <Compile Include="ashx\QuoteControl.ashx.cs">
      <DependentUpon>QuoteControl.ashx</DependentUpon>
    </Compile>
    <Compile Include="ashx\SupplierControl.ashx.cs">
      <DependentUpon>SupplierControl.ashx</DependentUpon>
    </Compile>
    <Compile Include="ashx\TemplateControl.ashx.cs">
      <DependentUpon>TemplateControl.ashx</DependentUpon>
    </Compile>
    <Compile Include="ashx\UserController.ashx.cs">
      <DependentUpon>UserController.ashx</DependentUpon>
    </Compile>
    <Compile Include="AssignFlow\FlowApply.aspx.cs">
      <DependentUpon>FlowApply.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AssignFlow\FlowApply.aspx.designer.cs">
      <DependentUpon>FlowApply.aspx</DependentUpon>
    </Compile>
    <Compile Include="AssignFlow\FlowAssign.aspx.cs">
      <DependentUpon>FlowAssign.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AssignFlow\FlowAssign.aspx.designer.cs">
      <DependentUpon>FlowAssign.aspx</DependentUpon>
    </Compile>
    <Compile Include="AssignFlow\FlowAssignDetail.aspx.cs">
      <DependentUpon>FlowAssignDetail.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AssignFlow\FlowAssignDetail.aspx.designer.cs">
      <DependentUpon>FlowAssignDetail.aspx</DependentUpon>
    </Compile>
    <Compile Include="DataManager\GroupConfig.aspx.cs">
      <DependentUpon>GroupConfig.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DataManager\GroupConfig.aspx.designer.cs">
      <DependentUpon>GroupConfig.aspx</DependentUpon>
    </Compile>
    <Compile Include="DataManager\QueryFile.aspx.cs">
      <DependentUpon>QueryFile.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DataManager\QueryFile.aspx.designer.cs">
      <DependentUpon>QueryFile.aspx</DependentUpon>
    </Compile>
    <Compile Include="DataManager\QueryHistoryRFQ.aspx.cs">
      <DependentUpon>QueryHistoryRFQ.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DataManager\QueryHistoryRFQ.aspx.designer.cs">
      <DependentUpon>QueryHistoryRFQ.aspx</DependentUpon>
    </Compile>
    <Compile Include="DataManager\QueryQuote.aspx.cs">
      <DependentUpon>QueryQuote.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DataManager\QueryQuote.aspx.designer.cs">
      <DependentUpon>QueryQuote.aspx</DependentUpon>
    </Compile>
    <Compile Include="DataManager\RateConfig.aspx.cs">
      <DependentUpon>RateConfig.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DataManager\RateConfig.aspx.designer.cs">
      <DependentUpon>RateConfig.aspx</DependentUpon>
    </Compile>
    <Compile Include="DataManager\SupplierManage.aspx.cs">
      <DependentUpon>SupplierManage.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DataManager\SupplierManage.aspx.designer.cs">
      <DependentUpon>SupplierManage.aspx</DependentUpon>
    </Compile>
    <Compile Include="DataManager\QueryHistoryQuote.aspx.cs">
      <DependentUpon>QueryHistoryQuote.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DataManager\QueryHistoryQuote.aspx.designer.cs">
      <DependentUpon>QueryHistoryQuote.aspx</DependentUpon>
    </Compile>
    <Compile Include="Flow\CreateRfq.aspx.cs">
      <DependentUpon>CreateRfq.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Flow\CreateRfq.aspx.designer.cs">
      <DependentUpon>CreateRfq.aspx</DependentUpon>
    </Compile>
    <Compile Include="index.aspx.cs">
      <DependentUpon>index.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="index.aspx.designer.cs">
      <DependentUpon>index.aspx</DependentUpon>
    </Compile>
    <Compile Include="Login.aspx.cs">
      <DependentUpon>Login.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Login.aspx.designer.cs">
      <DependentUpon>Login.aspx</DependentUpon>
    </Compile>
    <Compile Include="main.aspx.cs">
      <DependentUpon>main.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="main.aspx.designer.cs">
      <DependentUpon>main.aspx</DependentUpon>
    </Compile>
    <Compile Include="Supplier\AddQuote.aspx.cs">
      <DependentUpon>AddQuote.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Supplier\AddQuote.aspx.designer.cs">
      <DependentUpon>AddQuote.aspx</DependentUpon>
    </Compile>
    <Compile Include="Supplier\QuoteDetail.aspx.cs">
      <DependentUpon>QuoteDetail.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Supplier\QuoteDetail.aspx.designer.cs">
      <DependentUpon>QuoteDetail.aspx</DependentUpon>
    </Compile>
    <Compile Include="tool\CsvFile.cs" />
    <Compile Include="DataManager\AddCsvConfig.aspx.cs">
      <DependentUpon>AddCsvConfig.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DataManager\AddCsvConfig.aspx.designer.cs">
      <DependentUpon>AddCsvConfig.aspx</DependentUpon>
    </Compile>
    <Compile Include="DataManager\EditCsvConfig.aspx.cs">
      <DependentUpon>EditCsvConfig.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DataManager\EditCsvConfig.aspx.designer.cs">
      <DependentUpon>EditCsvConfig.aspx</DependentUpon>
    </Compile>
    <Compile Include="DataManager\FieldManage.aspx.cs">
      <DependentUpon>FieldManage.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DataManager\FieldManage.aspx.designer.cs">
      <DependentUpon>FieldManage.aspx</DependentUpon>
    </Compile>
    <Compile Include="DataManager\TemplateManage.aspx.cs">
      <DependentUpon>TemplateManage.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DataManager\TemplateManage.aspx.designer.cs">
      <DependentUpon>TemplateManage.aspx</DependentUpon>
    </Compile>
    <Compile Include="DataManager\TemplateManage2.aspx.cs">
      <DependentUpon>TemplateManage2.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DataManager\TemplateManage2.aspx.designer.cs">
      <DependentUpon>TemplateManage2.aspx</DependentUpon>
    </Compile>
    <Compile Include="Flow\AddProject.aspx.cs">
      <DependentUpon>AddProject.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Flow\AddProject.aspx.designer.cs">
      <DependentUpon>AddProject.aspx</DependentUpon>
    </Compile>
    <Compile Include="Flow\ConfirmPart.aspx.cs">
      <DependentUpon>ConfirmPart.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Flow\ConfirmPart.aspx.designer.cs">
      <DependentUpon>ConfirmPart.aspx</DependentUpon>
    </Compile>
    <Compile Include="Flow\CreateRfq2.aspx.cs">
      <DependentUpon>CreateRfq2.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Flow\CreateRfq2.aspx.designer.cs">
      <DependentUpon>CreateRfq2.aspx</DependentUpon>
    </Compile>
    <Compile Include="Flow\CustomerTrans.aspx.cs">
      <DependentUpon>CustomerTrans.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Flow\CustomerTrans.aspx.designer.cs">
      <DependentUpon>CustomerTrans.aspx</DependentUpon>
    </Compile>
    <Compile Include="Flow\EditProject.aspx.cs">
      <DependentUpon>EditProject.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Flow\EditProject.aspx.designer.cs">
      <DependentUpon>EditProject.aspx</DependentUpon>
    </Compile>
    <Compile Include="Flow\SendQuote.aspx.cs">
      <DependentUpon>SendQuote.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Flow\SendQuote.aspx.designer.cs">
      <DependentUpon>SendQuote.aspx</DependentUpon>
    </Compile>
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="ashx\HomeController.ashx.cs">
      <DependentUpon>HomeController.ashx</DependentUpon>
    </Compile>
    <Compile Include="ImportBom.aspx.cs">
      <DependentUpon>ImportBom.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ImportBom.aspx.designer.cs">
      <DependentUpon>ImportBom.aspx</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ashx\UploadExcel.ashx.cs">
      <DependentUpon>UploadExcel.ashx</DependentUpon>
    </Compile>
    <Compile Include="SystemManager\AddUser.aspx.cs">
      <DependentUpon>AddUser.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="SystemManager\AddUser.aspx.designer.cs">
      <DependentUpon>AddUser.aspx</DependentUpon>
    </Compile>
    <Compile Include="SystemManager\ChangePassWord.aspx.cs">
      <DependentUpon>ChangePassWord.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="SystemManager\ChangePassWord.aspx.designer.cs">
      <DependentUpon>ChangePassWord.aspx</DependentUpon>
    </Compile>
    <Compile Include="SystemManager\EditUser.aspx.cs">
      <DependentUpon>EditUser.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="SystemManager\EditUser.aspx.designer.cs">
      <DependentUpon>EditUser.aspx</DependentUpon>
    </Compile>
    <Compile Include="SystemManager\UserManage.aspx.cs">
      <DependentUpon>UserManage.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="SystemManager\UserManage.aspx.designer.cs">
      <DependentUpon>UserManage.aspx</DependentUpon>
    </Compile>
    <Compile Include="tool\SendMail.cs" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\RFQ.Dao\RFQ.Dao.csproj">
      <Project>{c6564944-359e-4928-bb3c-158a4f4674c1}</Project>
      <Name>RFQ.Dao</Name>
    </ProjectReference>
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>60434</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>https://localhost:44332/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>这台计算机上缺少此项目引用的 NuGet 程序包。使用“NuGet 程序包还原”可下载这些程序包。有关更多信息，请参见 http://go.microsoft.com/fwlink/?LinkID=322105。缺少的文件是 {0}。</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('packages\EntityFramework.6.4.4\build\EntityFramework.props')" Text="$([System.String]::Format('$(ErrorText)', 'packages\EntityFramework.6.4.4\build\EntityFramework.props'))" />
    <Error Condition="!Exists('packages\EntityFramework.6.4.4\build\EntityFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', 'packages\EntityFramework.6.4.4\build\EntityFramework.targets'))" />
  </Target>
  <Import Project="packages\EntityFramework.6.4.4\build\EntityFramework.targets" Condition="Exists('packages\EntityFramework.6.4.4\build\EntityFramework.targets')" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>