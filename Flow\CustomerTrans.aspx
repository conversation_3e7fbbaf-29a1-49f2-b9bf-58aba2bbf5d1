<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="CustomerTrans.aspx.cs" Inherits="WebApplication1.Flow.CustomerTrans" %>

<!DOCTYPE html>
<style>
    .table-container {
        width: 100%;
        overflow-x: auto;
        overflow-y: auto;
    }

    .layui-table-view {
        width: max-content; /* 确保表格宽度适应内容 */
    }
    
    
</style>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta charset="utf-8">
    <title>客户模板转换</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../lib/layui-v2.9.21/css/layui.css" media="all">
    <link rel="stylesheet" href="../css/public.css" media="all">
</head>
<body>
    <div class="layuimini-container layuimini-page-anim">
        <div class="layuimini-main">
            <!-- RFQ编号显示区域 -->
            <div class="layui-card" style="margin-bottom: 15px;">
                <div class="layui-card-header" style="background-color: #f8f8f8; border-left: 4px solid #1E9FFF;">
                    <i class="layui-icon layui-icon-form" style="color: #1E9FFF;"></i>
                    <span style="font-weight: bold; margin-left: 5px;">当前RFQ编号：</span>
                    <span id="currentRFQNo" style="color: #1E9FFF; font-weight: bold; font-size: 16px;">加载中...</span>
                </div>
            </div>

            <fieldset class="table-search-fieldset">
                <legend>搜索信息</legend>
                <div style="margin: 10px 10px 10px 10px">
                    <form class="layui-form layui-form-pane" id="rfqForm">

                        <div class="layui-form-item">
                            <label class="layui-form-label">客户编号</label>
                            <div class="layui-input-inline">
                                <select id="CustomerCode" lay-verify="required" lay-search="" lay-filter="CustomerCode">
                                </select>
                            </div>
                            <button type="button" class="layui-btn layui-btn-normal" id="uploadBtn">上传客户模版</button>
                            <%--<button type="button" class="layui-btn layui-btn-normal" id="setConfig">配置读取规则</button>--%>
                            <button type="button" class="layui-btn layui-btn-normal" id="EditHeader">编辑映射列</button>
                            <button type="button" class="layui-btn layui-btn-normal" id="Create">新建规则</button>
                            <button type="button" class="layui-btn layui-btn-normal" id="SaveAs">另存规则</button>
                            <input type="file" id="fileInput" style="display: none;">
                        </div>
                    </form>
                </div>


            </fieldset>

            <div class="table-container">
                <table class="layui-hide" id="bomTable" lay-filter="bomTableFilter"></table>
            </div>
            <div class="container">
                <div class="layui-input-block">
                    <button type="submit" id="prevstep" class="layui-btn" lay-submit lay-filter="data-prev-btn">
                        &emsp;上一步&emsp;
                                           
                    </button>
                    <button type="submit" id="nextstep" class="layui-btn" lay-submit lay-filter="data-next-btn">
                        &emsp;下一步&emsp;
                                           
                    </button>
                </div>
            </div>

            <%--<table class="layui-hide" id="combineTable" lay-filter="combineTableFilter"></table>--%>

            <!-- 表格操作工具栏模板 -->
            <script type="text/html" id="bomBar">
                {{#  if(d.Model1 !=null){ }}
    {{#  }else {}}
    <a class="layui-btn layui-btn-xs " lay-event="edit">关联数据</a>
                {{#  } }}
        
   
            </script>
<script type="text/html" id="FILE_UPLOADTpl">
  <button class="layui-btn layui-btn-xs FILE_UPLOAD-upload-btn" data-id="{{d.Id}}">上传</button>
  {{d.FILE_UPLOAD || '无文件'}}
</script>

<script type="text/html" id="FILE_UPLOAD2Tpl">
  <button class="layui-btn layui-btn-xs FILE_UPLOAD2-upload-btn" data-id="{{d.Id}}">上传</button>
  {{d.FILE_UPLOAD2 || '无文件'}}
</script>

<script type="text/html" id="FILE_UPLOAD3Tpl">
  <button class="layui-btn layui-btn-xs FILE_UPLOAD3-upload-btn" data-id="{{d.Id}}">上传</button>
  {{d.FILE_UPLOAD3 || '无文件'}}
</script>

            <script type="text/html" id="FILE_UPLOAD4Tpl">
  <button class="layui-btn layui-btn-xs FILE_UPLOAD4-upload-btn" data-id="{{d.Id}}">上传</button>
  {{d.FILE_UPLOAD4 || '无文件'}}
</script>

            <script type="text/html" id="FILE_UPLOAD5Tpl">
  <button class="layui-btn layui-btn-xs FILE_UPLOAD5-upload-btn" data-id="{{d.Id}}">上传</button>
  {{d.FILE_UPLOAD5 || '无文件'}}
</script>
        </div>

    </div>

<script src="../lib/layui-v2.9.21/layui.js" charset="utf-8"></script>
    <script src="../js/navigate.js" charset="utf-8"></script>
    <script src="../js/error-handler.js" charset="utf-8"></script>

    <script>
        layui.use(['form', 'table', 'upload', 'jquery'], function () {
            var form = layui.form,
                miniPage = layui.miniPage,
                table = layui.table,
                $ = layui.jquery,
                miniPage = layui.miniPage,
                layer = layui.layer,
                columns = [],
                upload = layui.upload,
                globalSelectedFields = {},
                counttable = 0;

            // 使用全局错误处理器的简化引用
            var showErrorDetails = ErrorHandler.showErrorDetails;
            var handleAjaxError = ErrorHandler.handleAjaxError;

            function getQueryParam(name) {
                // 保留原有的参数获取逻辑
                var hash = window.location.hash;
                var hashParams = hash.substring(hash.indexOf('?') + 1);
                var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
                var r = hashParams.match(reg);

                if (r != null) {
                    return decodeURIComponent(r[2]);
                }

                var queryParams = window.location.search.substr(1);
                r = queryParams.match(reg);
                if (r != null) {
                    return decodeURIComponent(r[2]);
                }

                return null;
            }

            // 从URL获取RFQNo
            var RFQNo = getQueryParam('RFQNo');
            console.log(RFQNo);

            // 显示RFQ编号
            if (RFQNo) {
                $('#currentRFQNo').text(RFQNo);
            } else {
                $('#currentRFQNo').text('未获取到RFQ编号').css('color', '#FF5722');
            }

            function getcustomer() {
                // 动态加载客户模板下拉框数据
                $.ajax({
                    url: '../ashx/ImportCustomer.ashx?action=GetCode&RFQNo=' + RFQNo,
                    type: 'POST',
                    success: function (data) {
                        try {
                            var codes = JSON.parse(data);
                            if (codes.code === 0) {
                                $('#CustomerCode').empty();
                                $('#CustomerCode').append(new Option('请选择客户编号', ''));
                                $.each(codes.CustomerCode, function (index, value) {
                                    $('#CustomerCode').append(new Option(value, value));
                                });
                                form.render('select');
                            } else {
                                showErrorDetails('加载客户编号', codes.msg || '获取客户编号失败', '服务器返回错误代码: ' + codes.code);
                            }
                        } catch (e) {
                            showErrorDetails('加载客户编号', '数据解析失败', '服务器返回的数据格式不正确: ' + data);
                        }
                    },
                    error: function (xhr, status, error) {
                        handleAjaxError(xhr, status, error, '加载客户编号');
                    }
                });
            }
            getcustomer();
            // 监听客户模板选择事件
            form.on('select(CustomerCode)', function (data) {

                var code = data.value;
                //if (code) {
                //    $.ajax({
                //        url: 'ashx/ImportCustomer.ashx?action=CheckMapping&CustomerCode=' + $('#CustomerCode').val(),
                //        method: 'POST',
                //        success: function (result) {
                //            var res = eval("(" + result + ")")
                //            if (res.code == 0) {
                //                getcolumndata(1);
                //            } else if (res.code == 2) {
                //                layer.msg("没有设置导入规则,请先设置");
                                globalSelectedFields = {};
                                EditRule();
                    //        }
                    //        else {
                    //            layer.msg(res.msg);
                    //        }
                    //    }
                    //});
                    //loadTableData(rfqNo);
                //}

                //} else {
                //    table.reload('dataTable', { data: [] });

                //}
            });

            



            // 初始化数据表格

            function getcolumndata(type) {  //type=1 加载数据和列 type=0 仅加载列
                $('button').prop('disabled', true);  // 禁用所有按钮
                var index = layer.load(1, { shade: [0.5, '#fff'] });  // 显示加载动画
                console.log('添加前');
                columns = [];
                $.ajax({
                    url: '../ashx/ImportCustomer.ashx?action=QueryConfig&CustomerCode=' + $('#CustomerCode').val() + '&RFQNo=' + RFQNo,
                    type: 'POST',
                    success: function (response) {
                        try {
                            var parsedResponse = JSON.parse(response);
                            if (parsedResponse.result == 'success') {
                                var fielddata = parsedResponse.col;
                                var modeldata = parsedResponse.models;
                            //console.log(modeldata);
                            columns.push({ field: 'Id', minWidth: 50, title: 'Id', hide: true });
                            if (fielddata.length > 0) {
                                //for (var key in fielddata) {

                                //    columns.push({ field: fielddata[key], minWidth: 150, title: `<button class="col-button" value="${fielddata[key]}">${fielddata[key]}</button>`});
                                //}
                                fielddata.forEach(function (fd) {
                                    columns.push({ field: fd.DatabaseField, align: 'center', minWidth: 170, title: `<button class="col-button layui-btn layui-btn-xs" value="${fd.DatabaseField}">${fd.DatabaseField}</button>` });
                                });
                            }
                            if (modeldata.length > 0) {
                                for (var key in modeldata) {

                                    columns.push({ field: modeldata[key], align: 'center', minWidth: 150, title: `<button class="qpa-button layui-btn layui-btn-xs" data-qpa="${+key + 1}" value="${modeldata[key]}">${modeldata[key]}</button>` });
                                }
                                //modeldata.forEach(function (md) {
                                //    columns.push({ field: md, minWidth: 100, title: `<button class="qpa-button" value="${md}">${md}</button>` });
                                //});
                            }


                            // 添加文件上传列
                            ['FILE_UPLOAD', 'FILE_UPLOAD2', 'FILE_UPLOAD3', 'FILE_UPLOAD4','FILE_UPLOAD5'].forEach(function (field) {
                                columns.push({
                                    field: field,
                                    title: field,
                                    width: 200,
                                    templet: '#' + field + 'Tpl'
                                    //templet: function (d) {
                                    //    var html = '<div>' + (d[field] || '无文件') + '</div>';
                                    //    html += '<button class="layui-btn file-upload-btn" data-id="' + d.Id + '" data-field="' + field + '">上传文件</button>';
                                    //    return html;
                                    //}
                                });
                            });
                            // 动态生成表头列
                            //var columns = [], i = 1;
                            //columns.push({ type: "checkbox", minWidth: 50 });
                            //columns.push({ field: 'Id', minWidth: 50, title: 'Id', hide: true });
                            //if (tabledata.length > 0) {
                            //    var firstRow = tabledata[0];
                            //    for (var key in firstRow) {
                            //        if (key == 'ID') { columns.push({ field: key, minWidth: 50, title: key, hide: true }); continue;}
                            //        if (key == 'ITEM' || key == 'CUST_PARTNUMBER' || key == 'BOM_DESC' || key == 'REF_BOM' || key == 'UOM_BOM' || key == 'Manufacturer' || key == 'Man_PartNumber' || key == 'Notes') {
                            //            columns.push({ field: key, minWidth: 150, title: key });
                            //        }
                            //        else {
                            //            columns.push({
                            //                field: key, minWidth: 100, title: `<button class="qty-button" data-qty="${i}">QTY1</button>` }); i += 1;}
                            //    }
                            //}
                            console.log('添加完成');
                                if (type == 1) {
                                    $.ajax({
                                        url: '../ashx/ImportCustomer.ashx?action=QueryBom&CustomerCode=' + $('#CustomerCode').val() + '&RFQNo=' + RFQNo,
                                        type: 'POST',
                                        success: function (bomdata) {
                                            try {
                                                var parsedBomData = JSON.parse(bomdata);
                                                if (parsedBomData.code != 1) {
                                                    rendertable(parsedBomData, columns);
                                                } else {
                                                    showErrorDetails('查询BOM数据', parsedBomData.msg || '查询BOM数据失败', '服务器返回错误代码: ' + parsedBomData.code);
                                                }
                                            } catch (e) {
                                                // 如果不是JSON格式，直接使用原始数据
                                                if (typeof bomdata === 'object') {
                                                    rendertable(bomdata, columns);
                                                } else {
                                                    showErrorDetails('查询BOM数据', '数据解析失败', '服务器返回的数据格式不正确: ' + bomdata);
                                                }
                                            }
                                            layer.close(index);
                                            $('button').prop('disabled', false);
                                        },
                                        error: function (xhr, status, error) {
                                            layer.close(index);
                                            $('button').prop('disabled', false);
                                            handleAjaxError(xhr, status, error, '查询BOM数据');
                                        }
                                    });
                                } else {
                                    rendertable([], columns);
                                    layer.close(index);
                                    $('button').prop('disabled', false);
                                }
                            } else {
                                showErrorDetails('查询配置信息', parsedResponse.msg || 'RFQNo为空,请先导入项目信息', '服务器返回: ' + response);
                                layer.close(index);
                                $('button').prop('disabled', false);
                            }
                        } catch (e) {
                            showErrorDetails('查询配置信息', '数据解析失败', '服务器返回的数据格式不正确: ' + response + '\n解析错误: ' + e.message);
                            layer.close(index);
                            $('button').prop('disabled', false);
                        }
                    },
                    error: function (xhr, status, error) {
                        layer.close(index);
                        $('button').prop('disabled', false);
                        handleAjaxError(xhr, status, error, '查询配置信息');
                    }
                });


            }

            function rendertable(data, columns) {
                table.render({
                    elem: '#bomTable',
                    //url: 'ashx/ImportCustomer.ashx?action=QueryBom',
                    cols: [columns],
                    data: data,
                    page: false,
                    limit: 2000,
                    height: 500,
                    done: function (res, curr, count) {
                        //console.log(columns);
                        // 表格渲染完成后的回调
                        counttable = count;
                        // 文件上传
                        ['FILE_UPLOAD', 'FILE_UPLOAD2', 'FILE_UPLOAD3', 'FILE_UPLOAD4', 'FILE_UPLOAD5'].forEach(function (field) {
                            //var id = $(this).data('id');
                            //var field = $(this).data('field');
                            upload.render({
                                elem: '.' + field + '-upload-btn',
                                url: '../ashx/ImportCustomer.ashx?action=UploadData', // 请替换为您的实际上传接口
                                accept: 'file', // 允许上传所有文件类型
                                auto: true,
                                before: function () {
                                    layer.load(); // 加载动画
                                    console.log(field + ' 上传开始');
                                },
                                done: function (res) {
                                    layer.closeAll('loading'); // 关闭加载动画
                                    console.log(field + ' 上传完成', res);
                                    // 上传完毕回调
                                    if (res.code == 0) {
                                        layer.msg('上传成功');
                                        // 更新表格数据
                                        var tr = this.item.closest('tr');
                                        var id = this.item.data('id');

                                        var index = tr.data('index');

                                        // 更新缓存中的数据
                                        var thisData = table.cache['bomTable'][index];
                                        thisData[field] = res.fileName;

                                        // 重新渲染这一行
                                        table.reload('bomTable', {
                                            data: table.cache['bomTable']
                                        });
                                        // 向后端发送更新请求
                                        $.ajax({
                                            url: '../ashx/ImportCustomer.ashx?action=UpdateFile',
                                            method: 'POST',
                                            data: {
                                                id: id,
                                                field: field,
                                                fileName: res.fileName
                                            },
                                            success: function (updateRes) {
                                                try {
                                                    var updateResult = JSON.parse(updateRes);
                                                    if (updateResult.code == 0) {
                                                        console.log('数据库更新成功');
                                                    } else {
                                                        showErrorDetails('更新文件信息', updateResult.msg || '数据库更新失败', '文件字段: ' + field + '\n文件名: ' + res.fileName);
                                                    }
                                                } catch (e) {
                                                    showErrorDetails('更新文件信息', '数据解析失败', '服务器返回的数据格式不正确: ' + updateRes);
                                                }
                                            },
                                            error: function (xhr, status, error) {
                                                handleAjaxError(xhr, status, error, '更新文件信息');
                                            }
                                        });
                                    } else {
                                        showErrorDetails('文件上传', res.msg || '上传失败', '文件字段: ' + field + '\n错误代码: ' + res.code);
                                    }
                                },
                                error: function (res) {
                                    layer.closeAll('loading');
                                    showErrorDetails('文件上传', '上传出错', '文件字段: ' + field + '\n错误信息: ' + (res.msg || '网络连接失败'));
                                }
                            });
                        });
                        //layer.closeAll('loading'); // 强制关闭loading
                    }
                });
            }

            

            


        //getcolumndata(1);
        //console.log('渲染前',columns);

        //table.render({
        //    elem: '#combineTable',
        //    url: 'ashx/ImportCustomer.ashx?action=QueryBom&type=combine',
        //    method: 'post',
        //    cols: [[
        //        { type: "checkbox", minWidth: 50 },
        //        {
        //            field: 'number', title: '序号', templet: function (d) {
        //                return d.LAY_INDEX;
        //            }
        //        },
        //        { field: 'CustPartNumber', minWidth: 100, title: '客户料号' },
        //        { field: 'BomDesc', minWidth: 150, title: '描述' },
        //        { field: 'RefBom', minWidth: 100, title: 'reference' },
        //        { field: 'UomBom', minWidth: 100, title: 'U/M' },
        //        { field: 'Model1', minWidth: 70, title: 'EAU1' },
        //        { field: 'Model2', minWidth: 70, title: 'EAU2' },
        //        { field: 'Model3', minWidth: 70, title: 'EAU3' },
        //        { field: 'Manufacturer', minWidth: 100, title: 'MFG' },
        //        { field: 'ManPartNumber', minWidth: 100, title: 'MPN' },
        //        { field: 'Notes', minWidth: 170, title: 'Remark' },
        //        { title: '操作', minWidth: 200, toolbar: '#combineBar', align: "center" },
        //        { field: 'Id', width: 50, title: 'guid', hide: true }
        //    ]],
        //    data: [],
        //    page: true,
        //    limit: 10,
        //    skin: 'line',
        //});

        // 点击上一步
        form.on('submit(data-prev-btn)', function (data) {
            $.ajax({
                url: '../ashx/ImportCustomer.ashx?action=GetNo',
                type: 'post',
                success: function (data) {
                    try {
                        var res = JSON.parse(data);
                        if (res.code === 0) {
                            navigateInIframe("../Flow/CreateRfq.aspx?RFQNo=" + encodeURIComponent(res.rfqNo), "新建RFQ流程-" + encodeURIComponent(res.rfqNo));
                        } else {
                            showErrorDetails('获取RFQ编号', res.msg || '获取RFQ编号失败', '服务器返回错误代码: ' + res.code);
                        }
                    } catch (e) {
                        showErrorDetails('获取RFQ编号', '数据解析失败', '服务器返回的数据格式不正确: ' + data);
                    }
                },
                error: function (xhr, status, error) {
                    handleAjaxError(xhr, status, error, '获取RFQ编号');
                }
            });
        });

        form.on('submit(data-next-btn)', function (data) {
            if (counttable == 0) {
                layer.msg('未导入bom信息', { icon: 0 });
                return;
            }
            // 弹出选择窗口
            layer.open({
                type: 1,
                title: '请选择优先级',
                content: `<div style="padding: 20px;">
                <p>请选择一个选项：</p>
                <button class="layui-btn" id="customerPriority">客户料号优先</button>
                <button class="layui-btn" id="mpnPriority">MPN优先</button></div>`,
                area: ['300px', '200px'],
                closeBtn: 0,
                shadeClose: false,
                success: function (layero, index) {
                    // 绑定按钮点击事件
                    $('#customerPriority, #mpnPriority').on('click', function () {
                        var priority = $(this).attr('id') === 'customerPriority' ? 'customer' : 'mpn';
                        layer.close(index);
                        proceedToNextStep(priority);
                    });
                }
            });
            //$.ajax({
            //    url: 'ashx/ImportCustomer.ashx?action=UpdateStatus',
            //    type: 'post',
            //    success: function (data) {
            //        console.log(data);
            //        var objdata = eval("(" + data + ")")
            //        if (objdata.result == "success") {
            //            //console.log(field);
            //            layer.msg(objdata.msg, { icon: 1, time: 2000 }, function () {
            //                window.location = "index.html#/Flow/xxx.aspx";
            //            })
            //        }
            //        else {
            //            layer.alert(objdata.msg);
            //        }

            //    },
            //    error: function (data) {
            //        layer.alert(objdata.msg);
            //    }
            //});
        });

        function proceedToNextStep(priority) {
            $.ajax({
                url: '../ashx/ImportCustomer.ashx?action=UpdateStatus&CustomerCode=' + $('#CustomerCode').val() + '&RFQNo=' + RFQNo,
                type: 'post',
                data: { priority: priority },
                success: function (data) {
                    try {
                        var objdata = JSON.parse(data);
                        if (objdata.result == "success") {
                            layer.msg(objdata.msg, { icon: 1, time: 2000 }, function () {
                                navigateInIframe("../Flow/ConfirmPart.aspx?RFQNo=" + RFQNo, "共用料确认-" + RFQNo);
                            });
                        } else {
                            showErrorDetails('更新状态', objdata.msg || '更新状态失败', '优先级设置: ' + priority + '\n服务器返回: ' + data);
                        }
                    } catch (e) {
                        showErrorDetails('更新状态', '数据解析失败', '服务器返回的数据格式不正确: ' + data);
                    }
                },
                error: function (xhr, status, error) {
                    handleAjaxError(xhr, status, error, '更新状态');
                }
            });
        }


        // 上传文件按钮点击事件
        $('#uploadBtn').on('click', function () {

            if ($('#CustomerCode').val() == "") {
                layer.msg('请先选择模板编号！', { icon: 2 });
                return;
            }
            else {
                $('#fileInput').click();
            }

        });
       
        //配置读取规则按钮点击事件
        $('#EditHeader').on('click', function () {
            if ($('#CustomerCode').val() == "") {
                layer.msg('请先选择客户编号！', { icon: 2 });
            }
            else {
                globalSelectedFields = {};
                EditRule();
            }

        });

            function EditRule() {
                
                var code = $('#CustomerCode').val();
                var allRulesData = []; // 存储所有规则数据的全局变量

                var index = layer.open({
                    title: '编辑映射关系',
                    type:2 ,
                    shade: 0.2,
                    maxmin: false,
                    shadeClose: true,
                    area: ['100%', '100%'],
                    content: '../DataManager/FieldManage.aspx',
                    //closeBtn: 0,  // 隐藏关闭按钮
                    success: function (layero, index) {

                        // 获取iframe页面的窗口对象
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        // 获取iframe页面的document对象
                        var iframeDoc = $(layero).find('iframe')[0].contentWindow.document;
                        form.render('select');
                        var allDatabaseFields = ['CUST_PARTNUMBER', 'BOM_DESC', 'REF_BOM', 'UOM_BOM', 'LOA_PRICE', 'CONTROL_FLAG', 'ITEM', 'NOTES', 'MANUFACTURER', 'MAN_PARTNUMBER', 'EAU','TOOL_COST','TOOL_CURR'];

                        // 获取所有规则数据的函数
                        // 使用Promise处理所有初始化数据加载
                        function initializeData() {
                            return Promise.all([
                                // 获取所有规则数据
                                new Promise((resolve, reject) => {
                                    $.ajax({
                                        url: '../ashx/TemplateControl.ashx?action=QueryAllRule&CustomerCode=' + code,
                                        type: 'GET',
                                        success: function (res) {
                                            allRulesData = JSON.parse(res).data || [];
                                            resolve();
                                        },
                                        error: function (error) {
                                            console.error('Error fetching all rules:', error);
                                            reject(error);
                                        }
                                    });
                                }),
                                // 获取模型字段
                                new Promise((resolve, reject) => {
                                    $.ajax({
                                        url: '../ashx/ImportCustomer.ashx?action=QueryModel&RFQNo=' + RFQNo,
                                        type: 'GET',
                                        success: function (newFields) {
                                            var objdata = JSON.parse(newFields);
                                            var addfields = [];
                                            objdata.forEach(function (model) {
                                                addfields.push(model.MODEL);
                                            });
                                            allDatabaseFields = [...new Set([...allDatabaseFields, ...addfields])];
                                            console.log('Updated database fields:', allDatabaseFields);
                                            resolve();
                                        },
                                        error: function (error) {
                                            console.error('Error fetching new database fields:', error);
                                            reject(error);
                                        }
                                    });
                                })
                            ]);
                        }
                        // 获取所有规则数据的函数
                        function fetchAllRules() {
                            $.ajax({
                                url: '../ashx/TemplateControl.ashx?action=QueryAllRule&CustomerCode=' + code,
                                type: 'GET',
                                success: function (res) {
                                    allRulesData = JSON.parse(res).data || [];
                                },
                                error: function (error) {
                                    console.error('Error fetching all rules:', error);
                                }
                            });
                        }
                        

                        //var selectedFields = {};

                        //var tableData = []; // 新增：用于存储表格数据

                        function getAvailableFields(currentValue) {
                            var selectedFields = {};
                            allRulesData.forEach(function (rule) {
                                console.log(rule.DatabaseField);
                                if (rule.DatabaseField) {
                                    selectedFields[rule.DatabaseField] = true;
                                }
                            });

                            return allDatabaseFields.filter(field =>
                                field === currentValue || !selectedFields[field]
                            );
                        }

                        // 验证函数
                        function validateSelects() {
                            var isValid = true;
                            var emptyFields = [];

                            // 使用全局的allRulesData进行验证
                            allRulesData.forEach(function (rule) {
                                if (!rule.DatabaseField) {
                                    isValid = false;
                                    emptyFields.push(rule.CsvHeader);
                                }
                            });

                            if (!isValid) {
                                layer.msg('以下字段未选择映射关系：' + emptyFields.join(', '), { icon: 2 });
                            }

                            return isValid;
                        }



                        //function updateSelectedFields() {
                        //    // 使用全局变量而不是局部变量
                        //    if (table && table.cache && table.cache.ruleTableid) {
                        //        layui.each(table.cache.ruleTableid, function (index, item) {
                        //            if (item.DatabaseField) {
                        //                globalSelectedFields[item.DatabaseField] = true;
                        //            }
                        //        });
                        //    } else {
                        //        layui.each(tableData, function (index, item) {
                        //            if (item.DatabaseField) {
                        //                globalSelectedFields[item.DatabaseField] = true;
                        //            }
                        //        });
                        //    }
                        //}

                        function renderDatabaseFieldSelect(d) {
                            var availableFields = getAvailableFields(d.DatabaseField);
                            console.log(availableFields);
                            var selectHtml = '<select name="databaseField" lay-filter="databaseField" data-row="' + d.LAY_TABLE_INDEX + '" lay-append-to="body">';
                            selectHtml += '<option value="">请选择</option>';
                            availableFields.forEach(function (field) {
                                var selected = (field === d.DatabaseField) ? 'selected' : '';
                                selectHtml += '<option value="' + field + '" ' + selected + '>' + field + '</option>';
                            });
                            selectHtml += '</select>';
                            return selectHtml;
                        }

                        // 初始化数据加载完成后再渲染表格
                        initializeData()
                            .then(() => {
                                // 渲染表格
                                iframeWin.layui.table.render({
                                    elem: '#ruleTableid',
                                    url: '../ashx/TemplateControl.ashx?action=QueryRule&CustomerCode=' + code,
                                    method: 'post',
                                    toolbar:'#toolbarrule',
                                    height: 'full-100',  
                                    css: [ // 设置单元格样式
                                    // 取消默认的溢出隐藏，并设置适当高度
                                    '.layui-table-cell{height: 50px; line-height: 40px;}',
                                    '.layui-table-cell .layui-colorpicker{width: 38px; height: 38px;}',
                                    '.layui-table-cell select{height: 80px; padding: 0 5px;}'
                                    ].join(''),
                                    cols: [[
                                        { type: "checkbox", width: 50, fixed: 'left' },
                                        { field: 'Id', width: 50, title: 'guid', hide: true },
                                        { field: 'CsvHeader', width: 250, title: 'Excel列' },
                                        { field: 'DatabaseField', width: 250, title: '数据库字段', templet: renderDatabaseFieldSelect }
                                    ]],
                                    page: false,
                                    skin: 'line',
                                    done: function () {
                                        form.render('select'); // 重新渲染select
                                    }
                                });
                            })
                            .catch(error => {
                                layer.msg('初始化数据失败，请刷新重试', { icon: 2 });
                                console.error('Initialization error:', error);
                            });



                        // 监听下拉框选择事件
                        iframeWin.layui.form.on('select(databaseField)', function (data) {
                            var $select = $(data.elem);
                            var $tr = $select.closest('tr');
                            var id = $tr.find('td[data-field="Id"]').text();
                            var newValue = data.value;
                            console.log(id);

                            // 更新本地数据
                            for (let i = 0; i < allRulesData.length; i++) {
                                if (allRulesData[i].Id === id) {
                                    allRulesData[i].DatabaseField = newValue;
                                    break;
                                }
                            }

                            

                            // 重新渲染整个表格
                            //table.reload('ruleTableid', {
                            //    tableData,
                            //    cols: [[
                            //        { type: "checkbox", width: 50 },
                            //        { field: 'Id', minWidth: 50, title: 'guid', hide: true },
                            //        { field: 'CsvHeader', minWidth: 150, title: 'Excel列' },
                            //        { field: 'DatabaseField', title: '数据库字段', width: 200, templet: renderDatabaseFieldSelect }
                            //    ]]
                            //});


                            //var id = table.cache.ruleTableid[rowIndex].Id;


                            // 发送更新请求到服务器
                            $.ajax({
                                url: '../ashx/TemplateControl.ashx?action=UpdateField',
                                type: 'POST',
                                data: { id: id, field: newValue },
                                success: function (res) {
                                    try {
                                        var result = JSON.parse(res);
                                        if (result.code === 0) {
                                            console.log("更新字段");
                                            iframeWin.layui.table.reload('ruleTableid');
                                            fetchAllRules();
                                        } else {
                                            showErrorDetails('更新字段映射', result.msg || '更新字段映射失败', '字段ID: ' + id + '\n新值: ' + newValue + '\n服务器返回: ' + res);
                                        }
                                    } catch (e) {
                                        showErrorDetails('更新字段映射', '数据解析失败', '服务器返回的数据格式不正确: ' + res);
                                    }
                                },
                                error: function (xhr, status, error) {
                                    handleAjaxError(xhr, status, error, '更新字段映射');
                                }
                            });
                        });




                        iframeWin.layui.upload.render({
                            elem: '#uploadtemple',
                            url: '../ashx/TemplateControl.ashx?action=UploadHeader&CustomerCode=' + code + '&RFQNo=' + RFQNo, // 后台处理上传的接口
                            accept: 'file', // 普通文件
                            exts: 'xls|xlsx', // 只允许上传Excel文件
                            before: function () {
                                // 禁用所有按钮
                                $('button').prop('disabled', true);
                                layer.load(); // 显示加载层
                            },
                            done: function (res) {
                                layer.closeAll('loading');
                                $('button').prop('disabled', false);
                                if (res.result && res.result.indexOf('success') != -1) {
                                    layer.msg('上传成功');
                                    fetchAllRules();
                                    iframeWin.layui.table.reload('ruleTableid');
                                    console.log(res);
                                } else {
                                    showErrorDetails('上传模板头部', res.result || '上传失败', '客户编号: ' + code + '\nRFQ编号: ' + RFQNo + '\n服务器返回: ' + JSON.stringify(res));
                                }
                            },
                            error: function (res) {
                                layer.closeAll('loading');
                                $('button').prop('disabled', false);
                                showErrorDetails('上传模板头部', '上传失败，请重试', '网络连接失败或服务器错误\n错误信息: ' + (res.msg || '未知错误'));
                            }
                        });

                        iframeWin.layui.table.on('toolbar(ruleTableFilter)', function (obj) {
                            if (obj.event == "delete") {
                                var checkStatus = iframeWin.layui.table.checkStatus('ruleTableid')
                                    , data = checkStatus.data;
                                console.log(data);

                                if (data.length == 0) {
                                    layer.msg('请选择要删除的项', { icon: 0 });
                                    return;
                                }
                                var nos = data.map(function (item) {
                                    return item.Id;
                                }).join(',');
                                $.ajax({
                                    url: '../ashx/TemplateControl.ashx?action=DeleteRule&CustomerCode=' + code,
                                    type: 'POST',
                                    data: { nos: nos },
                                    success: function (response) {
                                        try {
                                            var res = JSON.parse(response);
                                            console.log(res);
                                            if (res.result === 'success') {
                                                layer.msg('删除成功', { icon: 1 });
                                                fetchAllRules();
                                                iframeWin.layui.table.reload('ruleTableid', {
                                                    done: function (res, curr, count) {
                                                        if (curr > 1 && res.data.length === 0) {
                                                            curr = curr - 1;
                                                            iframeWin.layui.table.reload('ruleTableid', { page: { curr: curr }, })
                                                        }
                                                    }
                                                });
                                            } else {
                                                showErrorDetails('删除规则', res.msg || '删除失败', '删除的规则ID: ' + nos + '\n客户编号: ' + code + '\n服务器返回: ' + response);
                                            }
                                        } catch (e) {
                                            showErrorDetails('删除规则', '数据解析失败', '服务器返回的数据格式不正确: ' + response);
                                        }
                                    },
                                    error: function (xhr, status, error) {
                                        handleAjaxError(xhr, status, error, '删除规则');
                                    }
                                });
                            }
                        });

                        //监听提交
                        iframeWin.layui.form.on('submit(saveRule)', function (data) {
                            var field = data.field;
                            console.log(field);
                            $.ajax({
                                url: '../ashx/TemplateControl.ashx?action=AddRule&CustomerCode=' + code,
                                type: 'post',
                                data: field,
                                success: function (data) {
                                    try {
                                        var objdata = JSON.parse(data);
                                        if (objdata.result == "success") {
                                            layer.msg(objdata.msg, { icon: 1, time: 2000 }, function () {
                                                fetchAllRules();
                                                iframeWin.layui.table.reload('ruleTableid')
                                            })
                                        } else {
                                            showErrorDetails('添加规则', objdata.msg || '添加规则失败', '客户编号: ' + code + '\n服务器返回: ' + data);
                                        }
                                    } catch (e) {
                                        showErrorDetails('添加规则', '数据解析失败', '服务器返回的数据格式不正确: ' + data);
                                    }
                                },
                                error: function (xhr, status, error) {
                                    handleAjaxError(xhr, status, error, '添加规则');
                                }
                            });
                            return false;

                        });
                        $(iframeDoc).find('#submitRule').on('click', function () {
                            if (validateSelects()) {
                                // 发送更新请求到服务器
                                $.ajax({
                                    url: '../ashx/TemplateControl.ashx?action=SaveModelMapping&CustomerCode=' + code+'&RFQNo=' + RFQNo,
                                    type: 'POST',
                                    success: function (res) {
                                        try {
                                            var result = JSON.parse(res);
                                            if (result.code === 0) {
                                                UploadFile();
                                            } else {
                                                showErrorDetails('保存模型映射', result.msg || '保存模型映射失败', '客户编号: ' + code + '\nRFQ编号: ' + RFQNo + '\n服务器返回: ' + res);
                                            }
                                        } catch (e) {
                                            showErrorDetails('保存模型映射', '数据解析失败', '服务器返回的数据格式不正确: ' + res);
                                        }
                                        layer.close(index);
                                    },
                                    error: function (xhr, status, error) {
                                        layer.close(index);
                                        handleAjaxError(xhr, status, error, '保存模型映射');
                                    }
                                });
                                
                            }
                        });
                    },

                    //end: function () {
                        // 关闭弹窗时刷新主页面的表格
                        //getcolumndata(1);
                    //}
                });
                $(window).on("resize", function () {
                    layer.full(index);
                });
                return false;
            }

        $('#SaveAs').on('click', function () {
            if ($('#CustomerCode').val() == "") {
                layer.msg('请先选择客户编号！', { icon: 2 });
            }
            else {
                // 弹出输入框
                layer.prompt({
                    formType: 0, // 输入框类型，0 为文本输入
                    title: '请输入新的模板编号',
                    value: '默认名称' // 默认值，可根据需要设置
                }, function (value, index, elem) {
                    // 用户点击确认后的回调，value 为输入的值
                    console.log(value);
                    if (value == '' || value == '默认名称') {
                        layer.msg('模板编号不能为默认！', { icon: 2 });
                        return;
                    }
                    // 关闭弹出层
                    layer.close(index);
                    $('button').prop('disabled', true);  // 禁用所有按钮
                    var index2 = layer.load(1, { shade: [0.5, '#fff'] });  // 显示加载动画
                    // 进行保存操作，例如通过 Ajax 请求将名称发送到服务器
                    $.ajax({
                        url: '../ashx/ImportCustomer.ashx?action=SaveCustomer&CustomerCode=' + $('#CustomerCode').val(), // 替换为你的保存接口
                        type: 'POST',
                        data: { name: value }, // 将输入的名称传递给服务器
                        success: function (response) {
                            try {
                                var res = JSON.parse(response);
                                if (res.result == 'success') {
                                    layer.msg('保存成功！');
                                    getcustomer();
                                    rendertable([], []);
                                } else {
                                    showErrorDetails('另存规则', res.msg || '另存规则失败', '新模板名称: ' + value + '\n原客户编号: ' + $('#CustomerCode').val() + '\n服务器返回: ' + response);
                                }
                            } catch (e) {
                                showErrorDetails('另存规则', '数据解析失败', '服务器返回的数据格式不正确: ' + response);
                            }
                            layer.close(index2);
                            $('button').prop('disabled', false);
                        },
                        error: function (xhr, status, error) {
                            layer.close(index2);
                            $('button').prop('disabled', false);
                            handleAjaxError(xhr, status, error, '另存规则');
                        }
                    });
                });

            }
        });

            $('#Create').on('click', function () {
                
                    // 弹出输入框
                    layer.prompt({
                        formType: 0, // 输入框类型，0 为文本输入
                        title: '请输入新的模板编号',
                        value: '默认名称' // 默认值，可根据需要设置
                    }, function (value, index, elem) {
                        // 用户点击确认后的回调，value 为输入的值
                        console.log(value);
                        if (value == '' || value == '默认名称') {
                            layer.msg('模板编号不能为默认！', { icon: 2 });
                            return;
                        }
                        // 关闭弹出层
                        layer.close(index);
                        $('button').prop('disabled', true);  // 禁用所有按钮
                        var index2 = layer.load(1, { shade: [0.5, '#fff'] });  // 显示加载动画
                        // 进行保存操作，例如通过 Ajax 请求将名称发送到服务器
                        $.ajax({
                            url: '../ashx/ImportCustomer.ashx?action=CreateNewConfig&RFQNo=' + RFQNo, // 替换为你的保存接口
                            type: 'POST',
                            data: { name: value }, // 将输入的名称传递给服务器
                            success: function (response) {
                                try {
                                    var res = JSON.parse(response);
                                    if (res.result == 'success') {
                                        layer.msg('保存成功！');
                                        getcustomer();
                                        rendertable([], []);
                                    } else {
                                        showErrorDetails('创建新配置', res.msg || '创建配置失败', '配置名称: ' + value + '\n服务器返回: ' + response);
                                    }
                                } catch (e) {
                                    showErrorDetails('创建新配置', '数据解析失败', '服务器返回的数据格式不正确: ' + response);
                                }
                                layer.close(index2);
                                $('button').prop('disabled', false);
                            },
                            error: function (xhr, status, error) {
                                layer.close(index2);
                                $('button').prop('disabled', false);
                                handleAjaxError(xhr, status, error, '创建新配置');
                            }
                        });
                    });

                
            });

            function UploadFile(formData) {
                var index = layer.load(1, { shade: [0.5, '#fff'] });  // 显示加载动画
                $.ajax({
                    url: '../ashx/ImportCustomer.ashx?action=Upload&CustomerCode=' + $('#CustomerCode').val() + '&RFQNo=' + RFQNo,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (response) {
                        layer.close(index);
                        $('button').prop('disabled', false);
                        try {
                            var res = JSON.parse(response);
                            console.log(res);
                            if (res.result === 'success') {
                                layer.msg('文件上传成功', { icon: 1 });
                                getcolumndata(1);
                            } else if (res.result === 'fail') {
                                showErrorDetails('文件上传', res.msg || '文件上传失败', '客户编号: ' + $('#CustomerCode').val() + '\nRFQ编号: ' + RFQNo + '\n服务器返回: ' + response);
                            } else if (res.result === 'Nofile') {
                                showErrorDetails('文件上传', '无文件', '请选择要上传的文件');
                            } else {
                                // 尝试加载现有数据
                                getcolumndata(1);
                            }
                        } catch (e) {
                            showErrorDetails('文件上传', '数据解析失败', '服务器返回的数据格式不正确: ' + response + '\n解析错误: ' + e.message);
                        }
                        $('#fileInput').val('');
                    },
                    error: function (xhr, status, error) {
                        layer.close(index);
                        $('button').prop('disabled', false);
                        handleAjaxError(xhr, status, error, '文件上传');
                        $('#fileInput').val('');
                    }
                });
            }
        // 文件上传事件
        $('#fileInput').on('change', function () {
            $('button').prop('disabled', true);  // 禁用所有按钮
            
            var file = this.files[0];
            if (!file) return;
            console.log($('#fileInput').val());
            if ($('#fileInput').val().substring($('#fileInput').val().lastIndexOf(".")) != ".xlsx") {
                layer.msg('文件格式必须是xlsx', { icon: 2 });
                return;
            }
            var formData = new FormData();
            formData.append('file', file);

            UploadFile(formData);
        });

        // 点击表头按钮事件
        $(document).on('click', '.qpa-button', function () {
            var qtyIndex = $(this).data('qpa');
            var modelvalue = $(this).val();
            console.log(modelvalue);
            layer.open({
                type: 1,
                title: '选择Model',
                area: ['500px', '300px'],
                content: '<div id="model-list"></div>',
                success: function (layero, index) {
                    // 动态生成下拉框内容
                    $.ajax({
                        url: "../ashx/ImportCustomer.ashx?action=QueryModel&RFQNo=" + RFQNo,
                        type: 'GET',
                        success: function (models) {
                            try {
                                var datas = JSON.parse(models);
                                var content = '<form class="layui-form">';
                                content += '<div class="layui-form-item"><label class="layui-form-label">选择Model:</label>';
                                content += '<div class="layui-input-block">';
                                content += '<select id="model-select">';
                                datas.forEach(function (model) {
                                    if (model.MODEL == modelvalue) {
                                        content += `<option value="${model.MODEL}" selected>${model.MODEL}</option>`;
                                    } else {
                                        content += `<option value="${model.MODEL}">${model.MODEL}</option>`;
                                    }
                                });
                                content += '</select>';
                                content += '</div></div>';
                                content += '<div class="layui-form-item"><div class="layui-input-block">';
                                content += '<button class="layui-btn" id="save-model-btn">保存</button>';
                                content += '</div></div>';
                                content += '</form>';
                                $('#model-list').html(content);
                                form.render('select');
                            } catch (e) {
                                showErrorDetails('查询模型数据', '数据解析失败', '服务器返回的数据格式不正确: ' + models);
                            }
                        },
                        error: function (xhr, status, error) {
                            handleAjaxError(xhr, status, error, '查询模型数据');
                        }
                    });
                    // 点击保存按钮
                    $('#model-list').on('click', '#save-model-btn', function (e) {
                        e.preventDefault();
                        layer.confirm('确定保存选中的数据吗？(原有的该字段将与现有的字段互换)', function () {

                            var selectedModel = $('#model-select').val();
                            if (selectedModel) {
                                // 保存选择的model值到RFQ_TYPE_INFO表的BOMQTYFIELD列
                                $.ajax({
                                    url: '../ashx/ImportCustomer.ashx?action=SaveModel&RFQNo=' + RFQNo,
                                    type: 'POST',
                                    data: { qtyField: 'QPA' + qtyIndex, model: selectedModel },
                                    success: function (response) {
                                        try {
                                            var res = JSON.parse(response);
                                            if (res.result === 'success') {
                                                layer.msg('保存成功');
                                                layer.close(index);
                                                getcolumndata(1);
                                            } else {
                                                showErrorDetails('保存模型', res.msg || '保存模型失败', '数量字段: QPA' + qtyIndex + '\n模型: ' + selectedModel + '\n服务器返回: ' + response);
                                            }
                                        } catch (e) {
                                            showErrorDetails('保存模型', '数据解析失败', '服务器返回的数据格式不正确: ' + response);
                                        }
                                    },
                                    error: function (xhr, status, error) {
                                        handleAjaxError(xhr, status, error, '保存模型');
                                    }
                                });
                            }
                        });
                    });
                }
            });

        });

        $(document).on('click', '.col-button', function () {
            var colvalue = $(this).val();
            var code = $('#CustomerCode').val();
            console.log(colvalue);
            layer.open({
                type: 1,
                title: '选择映射列',
                area: ['500px', '300px'],
                content: '<div id="header-list"></div>',
                success: function (layero, index) {
                    // 动态生成下拉框内容
                    $.ajax({
                        url: "../ashx/ImportCustomer.ashx?action=QueryConfig&CustomerCode=" + code + "&Field=" + colvalue + "&RFQNo=" + RFQNo,
                        type: 'GET',
                        success: function (headers) {
                            try {
                                var parsedHeaders = JSON.parse(headers);
                                if (parsedHeaders.result === 'success') {
                                    var datas = parsedHeaders.col;
                                    var content = '<form class="layui-form">';
                                    content += '<div class="layui-form-item"><label class="layui-form-label">选择映射列:</label>';
                                    content += '<div class="layui-input-block">';
                                    content += '<select id="header-select">';
                                    datas.forEach(function (header) {
                                        if (header.DatabaseField == colvalue) {
                                            content += `<option value="${header.CsvHeader}" selected>${header.CsvHeader}</option>`;
                                        } else {
                                            content += `<option value="${header.CsvHeader}">${header.CsvHeader}</option>`;
                                        }
                                    });
                                    content += '</select>';
                                    content += '</div></div>';
                                    content += '<div class="layui-form-item"><div class="layui-input-block">';
                                    content += '<button class="layui-btn" id="save-header-btn">保存</button>';
                                    content += '</div></div>';
                                    content += '</form>';
                                    $('#header-list').html(content);
                                    form.render('select');
                                } else {
                                    showErrorDetails('查询配置头部', parsedHeaders.msg || '查询配置失败', '客户编号: ' + code + '\n字段: ' + colvalue + '\n服务器返回: ' + headers);
                                }
                            } catch (e) {
                                showErrorDetails('查询配置头部', '数据解析失败', '服务器返回的数据格式不正确: ' + headers);
                            }
                        },
                        error: function (xhr, status, error) {
                            handleAjaxError(xhr, status, error, '查询配置头部');
                        }
                    });
                    $('#header-list').on('click', '#save-header-btn', function (e) {
                        e.preventDefault();
                        layer.confirm('确定保存选中的数据吗？(原有的该字段将与现有的字段互换)', function () {
                            e.preventDefault();
                            var selectedHeader = $('#header-select').val();
                            if (selectedHeader) {
                                // 保存选择的model值到RFQ_CSV_FIELD表的BOMQTYFIELD列
                                $.ajax({
                                    url: '../ashx/ImportCustomer.ashx?action=SaveHeader',
                                    type: 'POST',
                                    data: { CustomerCode: code, field: colvalue, header: selectedHeader },
                                    success: function (response) {
                                        try {
                                            var res = JSON.parse(response);
                                            if (res.result === 'success') {
                                                layer.msg('保存成功');
                                                layer.close(index);
                                                getcolumndata(1);
                                            } else {
                                                showErrorDetails('保存头部映射', res.msg || '保存头部映射失败', '客户编号: ' + code + '\n字段: ' + colvalue + '\n头部: ' + selectedHeader + '\n服务器返回: ' + response);
                                            }
                                        } catch (e) {
                                            showErrorDetails('保存头部映射', '数据解析失败', '服务器返回的数据格式不正确: ' + response);
                                        }
                                    },
                                    error: function (xhr, status, error) {
                                        handleAjaxError(xhr, status, error, '保存头部映射');
                                    }
                                });
                            }
                        });
                    });
                }
            });

        });

        // 监听表格操作工具栏
        table.on('tool(bomTableFilter)', function (obj) {

            var selectdata = obj.data;
            var event = obj.event;
            var code = $('#CustomerCode').val();

            if (event === 'edit') {
                // 编辑逻辑

                var index = layer.open({
                    title: '关联型号',
                    type: 1,
                    shade: 0.2,
                    maxmin: true,
                    shadeClose: true,
                    area: ['600px', '700px'],
                    offset: ['150px', '550px'],
                    content: '../Flow/RelateModel.aspx',
                    success: function (layero, index) {
                        //console.log(data);
                        //form.val("addform");


                        table.render({
                            elem: '#typeTableid',
                            url: '../ashx/ImportCustomer.ashx?action=QueryModel&RFQNo=' + RFQNo,
                            width: 510,
                            method: 'post',
                            height: 500,
                            cols: [[
                                { type: "checkbox", width: 50 },
                                { field: 'Model', width: 100, title: '型号' },
                                { field: 'ModelDesc', width: 150, title: '描述' },
                                { field: 'EAU1', width: 70, title: 'EAU1' },
                                { field: 'EAU2', width: 70, title: 'EAU2' },
                                { field: 'EAU3', width: 70, title: 'EAU3' }
                            ]],
                            limit: 10,
                            page: true,
                            skin: 'line',

                        });


                        //监听提交
                        form.on('submit(relateinfo)', function (data) {

                            var checkStatus = table.checkStatus('typeTableid')
                                , checkdata = checkStatus.data;
                            console.log(checkdata);
                            if (data.length == 0) {
                                layer.msg('请选择要关联的项', { icon: 0 });
                                return false;
                            }
                            if (data.length > 1) {
                                layer.msg('关联项不能超过一个', { icon: 0 });
                                return;
                            }
                            $.ajax({
                                url: '../ashx/ImportCustomer.ashx?action=UpdateBom&CustomerCode=' + code + '&id=' + selectdata.Id + '&MFG=' + selectdata.Manufacturer + '&MPN=' + selectdata.ManPartNumber,
                                type: 'post',
                                data: { modelinfo: JSON.stringify(checkdata) },
                                success: function (data) {
                                    try {
                                        var objdata = JSON.parse(data);
                                        if (objdata.result == "success") {
                                            layer.msg(objdata.msg, { icon: 1, time: 2000 }, function () {
                                                layer.close(index);
                                                table.reload('bomTable');
                                                table.reload('combineTable');
                                            })
                                        } else {
                                            showErrorDetails('更新BOM信息', objdata.msg || '更新BOM信息失败', 'BOM ID: ' + selectdata.Id + '\n制造商: ' + selectdata.Manufacturer + '\n制造商料号: ' + selectdata.ManPartNumber + '\n服务器返回: ' + data);
                                        }
                                    } catch (e) {
                                        showErrorDetails('更新BOM信息', '数据解析失败', '服务器返回的数据格式不正确: ' + data);
                                    }
                                },
                                error: function (xhr, status, error) {
                                    handleAjaxError(xhr, status, error, '更新BOM信息');
                                }
                            });
                            return false;

                        });

                    },
                });
                //$(window).on("resize", function () {
                //    layer.full(index);
                //});

            }
        });

        table.on('tool(combineTableFilter)', function (obj) {
            var data = obj.data;
            if (obj.event === 'delete') {
                layer.confirm('真的删除行么', function (index) {
                    var nos = data.Id;
                    $.ajax({
                        url: '../ashx/ImportCustomer.ashx?action=DeleteRelate',
                        type: 'POST',
                        data: { nos: nos },
                        success: function (response) {
                            try {
                                var res = JSON.parse(response);
                                console.log(res);
                                if (res.result === 'success') {
                                    layer.msg('删除成功', { icon: 1 });
                                    table.reload('combineTable', {
                                        done: function (res, curr, count) {
                                            if (curr > 1 && res.data.length === 0) {
                                                curr = curr - 1;
                                                table.reload('combineTable', { page: { curr: curr }, })
                                            }
                                        }
                                    });
                                    table.reload('bomTable');
                                } else {
                                    showErrorDetails('删除关联', res.msg || '删除失败', '删除的ID: ' + nos + '\n服务器返回: ' + response);
                                }
                            } catch (e) {
                                showErrorDetails('删除关联', '数据解析失败', '服务器返回的数据格式不正确: ' + response);
                            }
                        },
                        error: function (xhr, status, error) {
                            handleAjaxError(xhr, status, error, '删除关联');
                        }
                    });


                    layer.closeAll();


                });
            }

        });
        }); </script>


</body>
</html>
