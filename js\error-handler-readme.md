# RFQ系统错误处理模块使用说明

## 概述

`error-handler.js` 是RFQ系统的统一错误处理模块，提供了标准化的错误显示和处理功能，可以在整个项目中复用。

## 引入方式

在HTML页面中引入错误处理模块：

```html
<script src="../js/error-handler.js" charset="utf-8"></script>
```

**注意：** 需要在layui.js之后引入，因为模块依赖layui的layer组件。

## 主要功能

### 1. 详细错误信息显示

```javascript
// 基本用法
ErrorHandler.showErrorDetails('操作名称', '错误信息', '详细信息');

// 带配置选项
ErrorHandler.showErrorDetails('文件上传', '上传失败', '文件大小超过限制', {
    title: '上传错误',
    width: '500px',
    height: '400px',
    showCopyButton: true,
    showContactInfo: true
});
```

### 2. AJAX错误处理

```javascript
$.ajax({
    url: '/api/example',
    type: 'POST',
    data: { key: 'value' },
    success: function(response) {
        // 处理成功响应
    },
    error: function(xhr, status, error) {
        ErrorHandler.handleAjaxError(xhr, status, error, '示例操作');
    }
});
```

### 3. JSON解析错误处理

```javascript
$.ajax({
    url: '/api/example',
    type: 'POST',
    success: function(response) {
        try {
            var data = JSON.parse(response);
            // 处理解析后的数据
        } catch (e) {
            ErrorHandler.handleParseError('示例操作', response, e);
        }
    }
});
```

### 4. 简单错误消息

```javascript
// 显示简单错误消息
ErrorHandler.showSimpleError('操作失败，请重试');

// 带选项的错误消息
ErrorHandler.showSimpleError('网络连接失败', {
    icon: 2,
    time: 5000
});
```

### 5. 成功消息

```javascript
// 显示成功消息
ErrorHandler.showSuccess('操作成功');

// 带回调的成功消息
ErrorHandler.showSuccess('保存成功', {
    icon: 1,
    time: 2000
});
```

## 完整示例

### 标准AJAX请求模式

```javascript
function saveData(formData) {
    $.ajax({
        url: '../ashx/DataHandler.ashx?action=Save',
        type: 'POST',
        data: formData,
        success: function(response) {
            try {
                var result = JSON.parse(response);
                if (result.code === 0 || result.result === 'success') {
                    ErrorHandler.showSuccess('保存成功');
                    // 执行成功后的操作
                } else {
                    ErrorHandler.showErrorDetails(
                        '保存数据', 
                        result.msg || '保存失败', 
                        '服务器返回: ' + response
                    );
                }
            } catch (e) {
                ErrorHandler.handleParseError('保存数据', response, e);
            }
        },
        error: function(xhr, status, error) {
            ErrorHandler.handleAjaxError(xhr, status, error, '保存数据');
        }
    });
}
```

### 文件上传错误处理

```javascript
layui.upload.render({
    elem: '#uploadBtn',
    url: '../ashx/FileHandler.ashx?action=Upload',
    done: function(res) {
        if (res.code === 0) {
            ErrorHandler.showSuccess('上传成功');
        } else {
            ErrorHandler.showErrorDetails(
                '文件上传',
                res.msg || '上传失败',
                '文件名: ' + fileName + '\n错误代码: ' + res.code
            );
        }
    },
    error: function(res) {
        ErrorHandler.showErrorDetails(
            '文件上传',
            '上传失败',
            '网络连接失败或服务器错误\n错误信息: ' + (res.msg || '未知错误')
        );
    }
});
```

## 配置选项

### showErrorDetails 配置选项

| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| title | string | '错误详情' | 弹窗标题 |
| width | string | '600px' | 弹窗宽度 |
| height | string | '500px' | 弹窗高度 |
| showCopyButton | boolean | true | 是否显示复制按钮 |
| showContactInfo | boolean | true | 是否显示联系信息 |

## 最佳实践

### 1. 错误信息分级
- **简单错误**: 使用 `showSimpleError()` 显示简短提示
- **复杂错误**: 使用 `showErrorDetails()` 显示详细信息
- **网络错误**: 使用 `handleAjaxError()` 统一处理

### 2. 错误信息内容
- **操作名称**: 使用中文，描述用户正在执行的操作
- **错误信息**: 用户友好的错误描述，避免技术术语
- **详细信息**: 包含技术细节，便于调试和技术支持

### 3. 错误上下文
在详细信息中包含：
- 操作参数
- 服务器响应
- 错误代码
- 相关的业务数据

### 4. 兼容性处理
模块自动处理以下兼容性问题：
- layui不可用时的降级处理
- 剪贴板API不支持时的降级复制
- 不同浏览器的差异

## 注意事项

1. **依赖关系**: 模块依赖layui的layer组件，确保正确引入顺序
2. **XSS防护**: 模块自动对用户输入进行HTML转义
3. **性能考虑**: 错误信息会被截断以避免过长的响应内容
4. **浏览器支持**: 支持IE9+及所有现代浏览器

## 更新日志

- **v1.0.0**: 初始版本，提供基础错误处理功能
- 支持详细错误显示、AJAX错误处理、JSON解析错误处理
- 提供复制功能和兼容性处理
