<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>错误处理模块使用示例</title>
    <link rel="stylesheet" href="lib/layui-v2.9.21/css/layui.css" media="all">
</head>
<body>
    <div class="layuimini-container layuimini-page-anim">
        <div class="layuimini-main">
            <div class="layui-card">
                <div class="layui-card-header">
                    <h2>错误处理模块使用示例</h2>
                </div>
                <div class="layui-card-body">
                    
                    <!-- 表单示例 -->
                    <fieldset class="layui-elem-field">
                        <legend>表单提交示例</legend>
                        <div class="layui-field-box">
                            <form class="layui-form" id="exampleForm">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">用户名</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="username" required lay-verify="required" placeholder="请输入用户名" class="layui-input">
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">邮箱</label>
                                    <div class="layui-input-block">
                                        <input type="email" name="email" required lay-verify="required|email" placeholder="请输入邮箱" class="layui-input">
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <div class="layui-input-block">
                                        <button class="layui-btn" lay-submit lay-filter="submitForm">提交</button>
                                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </fieldset>

                    <!-- 文件上传示例 -->
                    <fieldset class="layui-elem-field">
                        <legend>文件上传示例</legend>
                        <div class="layui-field-box">
                            <div class="layui-upload">
                                <button type="button" class="layui-btn" id="uploadBtn">选择文件</button>
                                <div class="layui-upload-list">
                                    <table class="layui-table">
                                        <thead>
                                            <tr><th>文件名</th><th>大小</th><th>状态</th></tr>
                                        </thead>
                                        <tbody id="uploadList"></tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </fieldset>

                    <!-- 数据操作示例 -->
                    <fieldset class="layui-elem-field">
                        <legend>数据操作示例</legend>
                        <div class="layui-field-box">
                            <div class="layui-btn-group">
                                <button class="layui-btn" id="loadData">加载数据</button>
                                <button class="layui-btn layui-btn-normal" id="saveData">保存数据</button>
                                <button class="layui-btn layui-btn-danger" id="deleteData">删除数据</button>
                            </div>
                            <div style="margin-top: 15px;">
                                <table class="layui-table" id="dataTable"></table>
                            </div>
                        </div>
                    </fieldset>

                </div>
            </div>
        </div>
    </div>

    <script src="lib/layui-v2.9.21/layui.js" charset="utf-8"></script>
    <script src="js/error-handler.js" charset="utf-8"></script>
    <script>
        layui.use(['form', 'upload', 'table', 'jquery'], function () {
            var form = layui.form;
            var upload = layui.upload;
            var table = layui.table;
            var $ = layui.jquery;

            // 表单提交示例
            form.on('submit(submitForm)', function(data) {
                var loadIndex = layer.load(1);
                
                // 模拟AJAX请求
                $.ajax({
                    url: '/api/user/save', // 这是一个不存在的接口，用于演示错误处理
                    type: 'POST',
                    data: data.field,
                    success: function(response) {
                        layer.close(loadIndex);
                        try {
                            var result = JSON.parse(response);
                            if (result.code === 0) {
                                ErrorHandler.showSuccess('用户信息保存成功');
                                $('#exampleForm')[0].reset();
                                form.render();
                            } else {
                                ErrorHandler.showErrorDetails(
                                    '保存用户信息',
                                    result.msg || '保存失败',
                                    '用户名: ' + data.field.username + '\n邮箱: ' + data.field.email + '\n服务器返回: ' + response
                                );
                            }
                        } catch (e) {
                            ErrorHandler.handleParseError('保存用户信息', response, e);
                        }
                    },
                    error: function(xhr, status, error) {
                        layer.close(loadIndex);
                        ErrorHandler.handleAjaxError(xhr, status, error, '保存用户信息');
                    }
                });
                
                return false; // 阻止表单跳转
            });

            // 文件上传示例
            upload.render({
                elem: '#uploadBtn',
                url: '/api/file/upload', // 这是一个不存在的接口，用于演示错误处理
                accept: 'file',
                multiple: true,
                before: function(obj) {
                    layer.load();
                },
                done: function(res, index, upload) {
                    layer.closeAll('loading');
                    if (res.code === 0) {
                        ErrorHandler.showSuccess('文件上传成功');
                        // 更新文件列表
                        var tr = '<tr><td>' + upload.name + '</td><td>' + (upload.size/1024).toFixed(1) + 'KB</td><td>上传成功</td></tr>';
                        $('#uploadList').append(tr);
                    } else {
                        ErrorHandler.showErrorDetails(
                            '文件上传',
                            res.msg || '上传失败',
                            '文件名: ' + upload.name + '\n文件大小: ' + upload.size + ' bytes\n错误代码: ' + res.code
                        );
                    }
                },
                error: function(index, upload) {
                    layer.closeAll('loading');
                    ErrorHandler.showErrorDetails(
                        '文件上传',
                        '上传失败',
                        '文件名: ' + upload.name + '\n网络连接失败或服务器错误'
                    );
                }
            });

            // 数据操作示例
            $('#loadData').click(function() {
                var loadIndex = layer.load(1);
                
                $.ajax({
                    url: '/api/data/list',
                    type: 'GET',
                    success: function(response) {
                        layer.close(loadIndex);
                        try {
                            var result = JSON.parse(response);
                            if (result.code === 0) {
                                // 渲染表格
                                table.render({
                                    elem: '#dataTable',
                                    data: result.data || [],
                                    cols: [[
                                        {field: 'id', title: 'ID', width: 80},
                                        {field: 'name', title: '名称', width: 120},
                                        {field: 'status', title: '状态', width: 80}
                                    ]]
                                });
                                ErrorHandler.showSuccess('数据加载成功');
                            } else {
                                ErrorHandler.showErrorDetails('加载数据', result.msg || '加载失败', '服务器返回: ' + response);
                            }
                        } catch (e) {
                            ErrorHandler.handleParseError('加载数据', response, e);
                        }
                    },
                    error: function(xhr, status, error) {
                        layer.close(loadIndex);
                        ErrorHandler.handleAjaxError(xhr, status, error, '加载数据');
                    }
                });
            });

            $('#saveData').click(function() {
                var loadIndex = layer.load(1);
                
                $.ajax({
                    url: '/api/data/save',
                    type: 'POST',
                    data: { name: '示例数据', status: '活跃' },
                    success: function(response) {
                        layer.close(loadIndex);
                        try {
                            var result = JSON.parse(response);
                            if (result.code === 0) {
                                ErrorHandler.showSuccess('数据保存成功');
                            } else {
                                ErrorHandler.showErrorDetails('保存数据', result.msg || '保存失败', '服务器返回: ' + response);
                            }
                        } catch (e) {
                            ErrorHandler.handleParseError('保存数据', response, e);
                        }
                    },
                    error: function(xhr, status, error) {
                        layer.close(loadIndex);
                        ErrorHandler.handleAjaxError(xhr, status, error, '保存数据');
                    }
                });
            });

            $('#deleteData').click(function() {
                layer.confirm('确定要删除选中的数据吗？', function(index) {
                    layer.close(index);
                    var loadIndex = layer.load(1);
                    
                    $.ajax({
                        url: '/api/data/delete',
                        type: 'POST',
                        data: { ids: '1,2,3' },
                        success: function(response) {
                            layer.close(loadIndex);
                            try {
                                var result = JSON.parse(response);
                                if (result.code === 0) {
                                    ErrorHandler.showSuccess('数据删除成功');
                                } else {
                                    ErrorHandler.showErrorDetails('删除数据', result.msg || '删除失败', '删除的ID: 1,2,3\n服务器返回: ' + response);
                                }
                            } catch (e) {
                                ErrorHandler.handleParseError('删除数据', response, e);
                            }
                        },
                        error: function(xhr, status, error) {
                            layer.close(loadIndex);
                            ErrorHandler.handleAjaxError(xhr, status, error, '删除数据');
                        }
                    });
                });
            });
        });
    </script>
</body>
</html>
